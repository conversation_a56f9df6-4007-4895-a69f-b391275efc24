import { FormEvent, JSX, useEffect } from 'react';
import { useStore } from '@betgames/bg-state-manager';
import {
    GameId,
    t,
    rgsAutoPlayEntity,
    partnerSettingsStore,
    betAmountEntity,
    formatToUserCurrency,
} from '@bg-shared';
import { SwitchConfigItem } from './SwitchConfigItem';
import { RangeConfigItem } from './RangeConfigItem';
import classes from './Content.module.scss';

interface IProps {
    gameId: GameId;
    onClose(): void;
}

const formatAmount = (value: number): ReturnType<typeof formatToUserCurrency> =>
    formatToUserCurrency(value);

export const Content = (props: IProps): JSX.Element => {
    const store = rgsAutoPlayEntity.store(props.gameId);
    const autoPlay = useStore(store);
    const betAmount = useStore(betAmountEntity.store(props.gameId));
    const partnerAutoPlay = partnerSettingsStore.getAutoPlay(props.gameId);

    useEffect(() => {
        store.updateAutoPlayFormDefaults();

        if (store.value.form.stopIfTotalLossExceeds < betAmount) {
            store.updateAutoPlayForm({
                stopIfTotalLossExceeds: betAmount,
            });
        }
    }, []);

    // TODO find solution to wrap in ScrollableContent
    //   problem is that RangeConfigItem component contains range input,
    //   gets cancelled touch event and sliding is not working

    return (
        <form
            onSubmit={(event: FormEvent) => {
                event.preventDefault();

                store.updateAutoPlay({
                    isActive: true,
                });

                props.onClose();
            }}
            className={classes.container}
        >
            <h2 className={classes.title}>{t.string('auto_play')}</h2>
            <div className={classes.checkboxBlock}>
                <SwitchConfigItem
                    label={t.string('stop_on_any_win')}
                    id="stop_on_any_win"
                    checked={autoPlay.form.stopOnAnyWin}
                    onChange={(checked) => {
                        store.updateAutoPlayForm({
                            stopOnAnyWin: checked,
                        });
                    }}
                    dataQa="stop_on_any_win"
                />
                <SwitchConfigItem
                    label={t.string('stop_on_bonus_game')}
                    id="stop_on_bonus_game"
                    checked={autoPlay.form.stopOnBonusGame}
                    onChange={(checked) => {
                        store.updateAutoPlayForm({
                            stopOnBonusGame: checked,
                        });
                    }}
                    dataQa="stop_on_bonus_game"
                />
            </div>
            <div className={classes.rangeBlock}>
                <RangeConfigItem
                    label={t.string('number_of_autoplay')}
                    value={autoPlay.form.numberOfAutoPlays}
                    onChange={(value) => {
                        store.updateAutoPlayForm({
                            numberOfAutoPlays: value,
                        });
                    }}
                    min={partnerAutoPlay.numberOfAutoplays.min}
                    max={partnerAutoPlay.numberOfAutoplays.max}
                    step={partnerAutoPlay.numberOfAutoplays.step}
                />
                <RangeConfigItem
                    label={t.string('stop_if_single_win_exceeds')}
                    value={autoPlay.form.stopIfSingleWinExceeds}
                    onChange={(value) => {
                        store.updateAutoPlayForm({
                            stopIfSingleWinExceeds: value,
                        });
                    }}
                    min={partnerAutoPlay.stopIfExceeds.min}
                    max={partnerAutoPlay.stopIfExceeds.max}
                    step={partnerAutoPlay.stopIfExceeds.step}
                    renderValue={formatAmount}
                />
                <RangeConfigItem
                    label={t.string('stop_if_loss_exceeds')}
                    value={autoPlay.form.stopIfTotalLossExceeds}
                    onChange={(value) => {
                        store.updateAutoPlayForm({
                            stopIfTotalLossExceeds: value,
                        });
                    }}
                    min={Math.max(partnerAutoPlay.stopIfExceeds.min, betAmount)}
                    max={partnerAutoPlay.stopIfExceeds.max}
                    step={partnerAutoPlay.stopIfExceeds.step}
                    renderValue={formatAmount}
                />
            </div>
            <div className={classes.buttonBlock}>
                <button type="submit" className={classes.button} data-qa="button-auto-play-start">
                    {t.string('start_autoplay')}
                </button>
            </div>
        </form>
    );
};
