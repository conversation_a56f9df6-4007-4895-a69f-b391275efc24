// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LoginProfile: Gallery should render component 1`] = `
<body>
  <div>
    <div
      class="container"
    >
      <div
        class="slider"
        data-qa="active-slide-0"
        style="transform: translateX(0%);"
      >
        <div
          class="slide"
        >
          <div>
            <div
              class="container imageContainer"
            >
              <div
                class="placeholder"
              />
              <picture
                class="picture"
              >
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-320.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-320.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-320.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-480.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-480.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-480.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-640.avif"
                  type="image/avif"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-640.webp"
                  type="image/webp"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_1-640.jpg"
                  type="image/jpeg"
                />
                <img
                  alt="gallery"
                  class="image image cover loading"
                  loading="eager"
                  src="/static/shared/images/gamification/wizard/default/step_1-640.jpg"
                />
              </picture>
            </div>
          </div>
          <div
            class="text"
          >
            <h3
              class="title"
            >
              This is your Heroes welcome!
            </h3>
            <div
              class="description"
            >
              Choose a nickname. Start your first challenge. Earn fantastic badges. Show off your HERO skills.
            </div>
          </div>
        </div>
        <div
          class="slide"
        >
          <div>
            <div
              class="container imageContainer"
            >
              <div
                class="placeholder"
              />
              <picture
                class="picture"
              >
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-320.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-320.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-320.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-480.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-480.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-480.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-640.avif"
                  type="image/avif"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-640.webp"
                  type="image/webp"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_2-640.jpg"
                  type="image/jpeg"
                />
                <img
                  alt="gallery"
                  class="image image cover loading"
                  loading="eager"
                  src="/static/shared/images/gamification/wizard/default/step_2-640.jpg"
                />
              </picture>
            </div>
          </div>
          <div
            class="text"
          >
            <h3
              class="title"
            >
              The world needs more Heroes
            </h3>
            <div
              class="description"
            >
              Level up to word up! Enhance your status with badges and word-up recognition
            </div>
          </div>
        </div>
        <div
          class="slide"
        >
          <div>
            <div
              class="container imageContainer"
            >
              <div
                class="placeholder"
              />
              <picture
                class="picture"
              >
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-320.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-320.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-320.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-480.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-480.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-480.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-640.avif"
                  type="image/avif"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-640.webp"
                  type="image/webp"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_3-640.jpg"
                  type="image/jpeg"
                />
                <img
                  alt="gallery"
                  class="image image cover loading"
                  loading="eager"
                  src="/static/shared/images/gamification/wizard/default/step_3-640.jpg"
                />
              </picture>
            </div>
          </div>
          <div
            class="text"
          >
            <h3
              class="title"
            >
              Unlock your inner Hero
            </h3>
            <div
              class="description"
            >
              Upskill, explore and challenge. It’s time to earn amazing badges and bragging honors.
            </div>
          </div>
        </div>
        <div
          class="slide"
        >
          <div>
            <div
              class="container imageContainer"
            >
              <div
                class="placeholder"
              />
              <picture
                class="picture"
              >
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-320.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-320.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 319px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-320.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-480.avif"
                  type="image/avif"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-480.webp"
                  type="image/webp"
                />
                <source
                  media="(max-width: 479px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-480.jpg"
                  type="image/jpeg"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-640.avif"
                  type="image/avif"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-640.webp"
                  type="image/webp"
                />
                <source
                  media="(min-width: 480px)"
                  srcset="/static/shared/images/gamification/wizard/default/step_4-640.jpg"
                  type="image/jpeg"
                />
                <img
                  alt="gallery"
                  class="image image cover loading"
                  loading="eager"
                  src="/static/shared/images/gamification/wizard/default/step_4-640.jpg"
                />
              </picture>
            </div>
          </div>
          <div
            class="text"
          >
            <h3
              class="title"
            >
              Let out the Hero inside you
            </h3>
            <div
              class="description"
            >
              Explore your adventurous side - and go BIG on Badges
            </div>
          </div>
        </div>
      </div>
      <button
        class="navLink left ripple"
        data-qa="button-welcome-wizard-left"
        type="button"
      >
        <span
          class="container chevronIcon"
        >
          <svg
            class="icon left"
          >
            chevron.svg
          </svg>
        </span>
      </button>
      <button
        class="navLink right ripple"
        data-qa="button-welcome-wizard-right"
        type="button"
      >
        <span
          class="container chevronIcon"
        >
          <svg
            class="icon right"
          >
            chevron.svg
          </svg>
        </span>
      </button>
      <div
        class="progress"
      >
        <div
          class="dot active"
        />
        <div
          class="dot"
        />
        <div
          class="dot"
        />
        <div
          class="dot"
        />
      </div>
    </div>
  </div>
</body>
`;
