import { sessionStore } from '@bg-shared/business';
import { DEFAULT_LANGUAGE } from '@bg-shared/constants';
import { useCallback, useState } from 'react';

const DEFAULT_SUB_PATH = '/default';

interface IImagePathWithFallback {
    path: string;
    onError(): void;
}

export const useImagePathWithFallback = (basePath: string): IImagePathWithFallback => {
    const isCustomLanguage = sessionStore.language !== DEFAULT_LANGUAGE;
    const initialPath = `${basePath}${
        isCustomLanguage ? `/${sessionStore.language}` : DEFAULT_SUB_PATH
    }`;

    const [path, setPath] = useState(initialPath);

    const handleError = useCallback(() => {
        setPath(`${basePath}${DEFAULT_SUB_PATH}`);
    }, [basePath]);

    return { path, onError: handleError };
};
