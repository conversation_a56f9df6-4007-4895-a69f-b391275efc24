import { merge, omit } from 'lodash-unified';
import currency from 'currency.js';
import { Store } from '@betgames/bg-state-manager';
import { StoresEntity } from '@bg-shared/business/StoreFeature';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { GameId } from '@bg-shared/enums/GameId';
import { IRGSAutoPlay, IRGSAutoPlayForm } from './interfaces';

const initialAutoPlay: IRGSAutoPlay = {
    isActive: false,
    currentRound: 0,
    totalWin: 0,
    lastWin: 0,
    betInfo: [],
    form: {
        stopOnAnyWin: false,
        stopOnBonusGame: false,
        numberOfAutoPlays: 0,
        stopIfSingleWinExceeds: 0,
        stopIfTotalLossExceeds: 0,
    },
};

export class RGSAutoPlayEntity extends Store<IRGSAutoPlay> {
    private defaultsInitialized = false;

    constructor(private readonly gameId: GameId) {
        super({
            key: `CasinoAutoplay/${gameId}`,
            default: initialAutoPlay,
        });
    }

    public updateAutoPlay(value: Partial<IRGSAutoPlay>): void {
        this.update((draft) => {
            merge(draft, value);
        });
    }

    public updateAutoPlayForm(value: Partial<IRGSAutoPlayForm>): void {
        this.update((draft) => {
            merge(draft.form, value);
        });
    }

    public updateAutoPlayFormDefaults(): void {
        if (this.defaultsInitialized) {
            return;
        }

        const partnerAutoPlay = partnerSettingsStore.getAutoPlay(this.gameId);

        this.updateAutoPlayForm({
            stopOnAnyWin: partnerAutoPlay.stopOnAnyWin,
            stopOnBonusGame: partnerAutoPlay.stopOnBonusGame,
            numberOfAutoPlays: partnerAutoPlay.numberOfAutoplays.default,
            stopIfSingleWinExceeds: partnerAutoPlay.stopIfExceeds.default,
            stopIfTotalLossExceeds: partnerAutoPlay.stopIfExceeds.default,
        });

        this.defaultsInitialized = true;
    }

    public clearAutoPlay(): void {
        this.update(initialAutoPlay);
    }

    public stopAutoPlay(): void {
        this.updateAutoPlay(omit(initialAutoPlay, ['form']));
    }

    public updateAutoPlayResult(stake: number, amountWon: number): void {
        if (!this.value.isActive) {
            return;
        }

        this.update((draft) => {
            // sum all losings (negative total winnings)
            if (stake > amountWon) {
                draft.totalWin = currency(draft.totalWin).subtract(stake).value;
            }
            draft.lastWin = amountWon;
        });
    }

    public reset(): void {
        super.reset();
        this.defaultsInitialized = false;
    }
}

export const rgsAutoPlayEntity = new StoresEntity(RGSAutoPlayEntity);
