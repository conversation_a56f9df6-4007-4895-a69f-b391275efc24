import { gsap } from 'gsap';
import * as PIXI from 'pixi.js';
import { Assets, Container, Point, Renderer } from 'pixi.js';
import { PixiPlugin } from 'gsap/PixiPlugin';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import {
    formatToUserCurrency,
    GameId,
    getSkywardItemColor,
    IPlayerWonModel,
    playerWonEntity,
} from '@bg-shared';
import { ClassicGameId } from 'betgames-contract-js';
import { Store } from '@betgames/bg-state-manager';
import Highlight from './objects/Multiplier/Highlight';
import Multiplier from './objects/Multiplier/Multiplier';
import { SkyPlane } from './objects/Plane/SkyPlane';
import {
    BACKGROUND_PLANE_START_POSITION,
    BACKGROUND_SIDE_MARGIN,
    GAME_HEIGHT,
    PLANE_ANIM_TIMING,
} from './config/config';
import { BackgroundController } from './controllers/BackgroundController';
import { PlayerWonMessageExperience } from './objects/PlayerWonMessage/Experience';
import { brandedConfig, CONFIGURABLE } from './utils/BrandedConfig';
import { skywardSoundsService } from '../shared/business/Sounds';
import { GameState, PlaneScene } from './enums/SkywardPixiEnums';
import { skywardEntity, SkywardStore } from './business/services/Skyward/Skyward.entity';
import { ISkywardModel } from './business/services/Skyward/interfaces';
import { ServerGameState } from './business/ws/enums';
import { FLY_AWAY_MESSAGE_DURATION } from '../shared/constants';
import { MIN_MULTIPLIER } from './business/constants';

export interface ISkywardPixiOptions {
    stage: Container;
    width: number;
    height: number;
    renderer: Renderer;
    animationEnabled: boolean;
}

class SkywardPixi {
    width: number;

    height: number;

    isReady = false;

    stage: Container;

    renderer: Renderer;

    skyPlane: SkyPlane;

    backgroundController: BackgroundController;

    highlight: Highlight;

    multiplier: Multiplier;

    currentGameState = GameState.planeInStartPosition;

    wonMessage: PlayerWonMessageExperience;

    private animationEnabled = true;

    private gameStore: SkywardStore;

    private playerWonStore: Store<IPlayerWonModel>;

    private unsubscribes: Array<() => void> = [];

    private lastRunId: string | null = null;

    private isAnimatingEndGame = false;

    private pendingReset: ISkywardModel | null = null;

    constructor({ stage, width, height, renderer, animationEnabled }: ISkywardPixiOptions) {
        this.stage = stage;
        this.width = width;
        this.height = height;
        this.renderer = renderer;
        this.animationEnabled = animationEnabled;
        this.gameStore = skywardEntity.store(GameId.SKYWARD_DELUXE);
        this.playerWonStore = playerWonEntity.store(ClassicGameId.SKYWARD_DELUXE);

        gsap.registerPlugin(PixiPlugin);
        gsap.registerPlugin(MotionPathPlugin);
        PixiPlugin.registerPIXI(PIXI);

        this.subscribeToGameData();
        this.subscribeToPlayerWon();
    }

    private subscribeToGameData() {
        const unsubscribe = this.gameStore.subscribe((state) => {
            this.handleGameStateChange(state);
        });
        this.unsubscribes.push(unsubscribe);
    }

    private subscribeToPlayerWon() {
        const unsubscribe = this.playerWonStore.subscribe((state) => {
            this.showMessage(state.amountWon);
        });
        this.unsubscribes.push(unsubscribe);
    }

    private handleGameStateChange(data: ISkywardModel) {
        if (!this.isReady || !data.state) {
            return;
        }

        // If animating end game, queue any pending state changes
        if (this.isAnimatingEndGame) {
            // Only queue if it's a new game (different runId)
            if (data.runId !== this.lastRunId) {
                this.pendingReset = data;
            }
            return;
        }

        // Detect new game run
        if (data.runId !== this.lastRunId) {
            this.lastRunId = data.runId;
            this.handleNewRun();
        }

        // Update multiplier during flight
        if (
            data.state === ServerGameState.Running &&
            this.currentGameState === GameState.planeFlying
        ) {
            this.updateMultiplierValue(data.multiplier || 0);
        }

        switch (data.state) {
            case ServerGameState.ReadyToBet:
                this.handleReadyToBet();
                break;

            case ServerGameState.Running:
                this.handleRunning();
                break;

            case ServerGameState.RunCancelled:
            case ServerGameState.RunFailed:
                this.handleCancelledOrFailed();
                break;

            case ServerGameState.RunEnded:
                this.handleRunEnded(data);
                break;
            default:
                break;
        }
    }

    private handleNewRun() {
        if (this.currentGameState !== GameState.planeInStartPosition) {
            this.resetGame();
        }
    }

    private handleReadyToBet() {
        // Ensure we're in start position
        if (this.currentGameState !== GameState.planeInStartPosition) {
            this.resetGame();
        }
    }

    private handleRunning() {
        // Start the game if we haven't already
        if (this.currentGameState === GameState.planeInStartPosition) {
            this.startGame();
        }
    }

    private handleCancelledOrFailed() {
        // End the game immediately without normal ending animation
        if (this.currentGameState === GameState.planeFlying) {
            this.endGameImmediate();
        }
    }

    private handleRunEnded(data?: ISkywardModel) {
        // Handle instant crash case - game never reached "running" state
        if (this.currentGameState === GameState.planeInStartPosition && data?.multiplier) {
            this.startGameInstant(data.multiplier);
        }

        if (this.currentGameState === GameState.planeFlying) {
            // Update to final multiplier value before ending the game
            if (data?.multiplier) {
                this.updateMultiplierValue(data.multiplier);
            }
            this.endGameWithAnimation();
        }
    }

    private startGameInstant(multiplier: number) {
        this.backgroundController.enablePath();
        this.backgroundController.startGame();
        this.currentGameState = GameState.planeFlying;

        // Skip plane starting up animation
        this.skyPlane.setToFlyingPosition(0.1);
        this.skyPlane.startPropellerOnly();

        // Show multiplier immediately
        this.updateMultiplierValue(multiplier);
    }

    private startGame() {
        const currentData = this.gameStore.value;
        const currentMultiplier = currentData?.multiplier || 0;

        // Always update multiplier display immediately when game starts
        if (currentMultiplier >= MIN_MULTIPLIER) {
            this.updateMultiplierValue(currentMultiplier);
        }

        // If game is already in progress, skip initial animations
        if (currentMultiplier > MIN_MULTIPLIER) {
            // Skip straight to flying state
            this.backgroundController.enablePath();
            this.backgroundController.startGame();
            this.currentGameState = GameState.planeFlying;

            const elapsedTime = currentData.time - currentData.start;
            const totalFlightTime = PLANE_ANIM_TIMING[PlaneScene.FLYING_UP].duration;
            const progress = Math.min(elapsedTime / totalFlightTime, 1);
            this.skyPlane.setToFlyingPosition(progress);
            this.backgroundController.seek(elapsedTime);

            // Start propeller animation without engine start
            this.skyPlane.startPropellerOnly();

            skywardSoundsService.playPropeller();
        } else {
            // Normal game start from beginning
            this.backgroundController.enablePath();
            this.skyPlane.startPlaneAnimation();
            this.backgroundController.startGame();
            this.currentGameState = GameState.planeFlying;
            skywardSoundsService.playPropeller();
        }
    }

    private endGameWithAnimation() {
        this.isAnimatingEndGame = true;
        this.skyPlane.flyAwayAnimation();
        this.currentGameState = GameState.endGame;
        this.backgroundController.endGame();
        this.multiplier.endGame();
        this.highlight?.endGame();
        skywardSoundsService.playFlyAway();

        // Wait for animation to complete before processing pending reset
        setTimeout(() => {
            this.isAnimatingEndGame = false;

            // Process any pending reset
            if (this.pendingReset) {
                const pendingData = this.pendingReset;
                this.pendingReset = null;
                this.handleGameStateChange(pendingData);
            }
        }, FLY_AWAY_MESSAGE_DURATION);
    }

    private endGameImmediate() {
        // Immediate end without fly away animation for cancelled/failed games
        this.currentGameState = GameState.endGame;
        this.backgroundController.endGame();
        this.multiplier.endGame();
        this.highlight?.endGame();
    }

    private resetGame() {
        this.skyPlane.reset();
        this.currentGameState = GameState.planeInStartPosition;
        this.backgroundController.reset();
        this.multiplier.reset();
        this.highlight?.reset();
    }

    private updateMultiplierValue(multiplier: number) {
        const colorName = getSkywardItemColor(multiplier);
        this.highlight?.update(CONFIGURABLE.multiplierColorMap[colorName]);
        this.multiplier.update(multiplier, colorName);
    }

    public initialize = async () => {
        await Promise.all([Assets.load(CONFIGURABLE.plane.texture), this.initBackground()]);
        this.createHighlight();
        this.createPlane();
        this.createMultiplier();
        this.createWonMessage();

        this.isReady = true;

        // Check current state after initialization
        const currentData = this.gameStore.value;
        if (currentData.state) {
            this.handleGameStateChange(currentData);
        }
    };

    public onResize = () => {
        this.highlight?.onResize();
        this.multiplier?.onResize();
        this.wonMessage?.onResize();
        this.skyPlane?.onResize();
        this.backgroundController?.onResize();
    };

    private createPlane() {
        this.skyPlane = new SkyPlane(this.stage, this.renderer, this.animationEnabled);
        this.skyPlane.setPosition(
            new Point(
                BACKGROUND_SIDE_MARGIN + BACKGROUND_PLANE_START_POSITION,
                GAME_HEIGHT - BACKGROUND_SIDE_MARGIN,
            ),
        );
    }

    private createWonMessage() {
        this.wonMessage = new PlayerWonMessageExperience(this.stage, this.renderer);
        this.wonMessage.init();
    }

    private async initBackground() {
        this.backgroundController = new BackgroundController(
            this.stage,
            this.animationEnabled,
            this.renderer,
            () => this.skyPlane,
        );
        await this.backgroundController.init();
    }

    private createMultiplier() {
        this.multiplier = new Multiplier(this.stage, this.renderer);
    }

    private createHighlight() {
        if (brandedConfig.isPremium()) {
            return;
        }
        this.highlight = new Highlight(this.stage, this.renderer);
    }

    public setAnimationEnabled(enabled: boolean) {
        this.animationEnabled = enabled;
        this.backgroundController.setAnimationEnabled(enabled);
        this.skyPlane?.setVisible(enabled);
    }

    public showMessage = (amount: string) => {
        if (!this.isReady || !amount) {
            return;
        }
        this.wonMessage.run(`+ ${formatToUserCurrency(parseFloat(amount))}`);
    };

    public update = (delta: number) => {
        this.backgroundController.update(delta, this.skyPlane.pos, this.currentGameState);

        // Update visual elements based on current multiplier from store
        if (this.currentGameState === GameState.planeFlying) {
            const storeData = this.gameStore.value;
            if (storeData.multiplier !== undefined) {
                this.updateMultiplierValue(storeData.multiplier);
            }
        }
    };

    // When game is unmounted, gsap tries to update properties of not existing objects
    private killAllTweens() {
        gsap.globalTimeline.getChildren().forEach((child) => {
            child.kill();
        });
    }

    public dispose = () => {
        this.unsubscribes.forEach((unsubscribe) => unsubscribe());
        this.unsubscribes = [];

        this.highlight?.dispose();
        this.backgroundController?.dispose();
        this.multiplier?.dispose();
        this.wonMessage?.dispose();
        this.killAllTweens();

        this.highlight = null;
        this.backgroundController = null;
        this.multiplier = null;
        this.stage = null;
        this.renderer = null;
        this.isReady = false;
    };
}

export default SkywardPixi;
