import type { EventManager } from '@bg-services';
import type { ICoordinates, IFreePlayAvailable, IFreePlayInfo } from '@bg-shared';
import type { HorizontalAlign, VerticalAlign } from '@bg-services/Pixi/LayoutManagerV2';
import type { Filter, Texture } from 'pixi.js';
import type {
    IGameConfig,
    IBalance,
    IExtendedTokenData,
    IGameRoundInfo,
    IGameStateCredit,
} from '@bg-shared/business/RGSGameApi/interfaces';
import type {
    LuckyLennyEvents,
    BELuckyLennyItems,
    NextAction,
    SymbolType,
    LuckyLennyGameId,
} from './enums';
import { LAYER_ORDER } from './LuckyLennyExperience/config';

// ---------------------------------------------------------------------------
// Event manager alias used across components
// ---------------------------------------------------------------------------

export type ILuckyLennyEventManager = EventManager<LuckyLennyEvents>;

// Cleaning up legacy duplicate types

export type ILuckyLennyResultItems = BELuckyLennyItems[];

// ---------------------------------------------------------------------------
// Front-end simplified result (array of symbol types) used by visual layer
// ---------------------------------------------------------------------------

export interface ILuckyLennyGameConfig extends IGameConfig {
    nextAction?: NextAction;
    lastRoundResult?: IRoundResult;
}

export type ILuckyLennyResult = SymbolType[];

interface IWin {
    length: number;
    symbol: number;
    multiplier?: number;
    startIndex: number;
}

export interface IRoundResult {
    type: 'standard' | 'gameboard' | 'wheel';
    wins: IWin[];
    closed: boolean;
    symbols: number[];
    bonusRounds: IRoundResult['type'][];
    totalMultiplier: number;
    roundVariables?: {
        setIndex?: number;
        wheelSymbols?: IWheelSymbolData[];
    };
}

export interface IGameResult {
    nextAction: NextAction;
    nextActions: NextAction[];
    roundResult: IRoundResult;
}

export interface IGameStateDebit {
    hash: string;
    type: 'debit';
    result: {
        winAmount: number;
        info: {
            bet: number;
            result: IGameResult;
            rngCallId: string[];
        };
    };
    processedOn: string;
}

export interface IGameStateGenericWin {
    hash: string;
    type: 'genericwin';
    result: {
        winAmount: number;
        info: {
            bet: number;
            result: IGameResult;
            rngCallId: string[];
        };
    };
    processedOn: string;
}

// TODO check if needed
interface ILuckyLennyGameRoundInfo {
    result?: {
        symbols: ILuckyLennyResultItems;
        multiplier: number;
        nextFeature: NextAction;
        rngCallIds: string[];
    };
}

export interface IPlayResponse {
    tokenData: IExtendedTokenData;
    balance: IBalance;
    amountToCollect: number;
    amountCredited: number;
    gameRoundInfo: IGameRoundInfo & ILuckyLennyGameRoundInfo;
    gameState: (IGameStateDebit | IGameStateGenericWin | IGameStateCredit)[];
    nextAction?: NextAction;
    lastRoundResult: IRoundResult;
    freePlaysAvailable?: IFreePlayAvailable[];
    freePlayInfo?: IFreePlayInfo;
}

export interface IExperienceOptions {
    canvas: HTMLCanvasElement;
    eventManager?: ILuckyLennyEventManager;
    enableDebug?: boolean;
    onLoadComplete?: () => void;
    width: number;
    height: number;
}

export interface ILuckyLennyResultEvent {
    result: ILuckyLennyResult;
    stake: number;
}

export type ILuckyLennyBonusGameboardClickEvent = number;

export interface ILuckyLennyWinEvent {
    winAmount?: number;
    amount?: number;
    multiplier: number;
    stake?: number;
    showPlusReSpin?: boolean;
}

export interface ILuckyLennyTotalWinEvent {
    amount: number;
}

export interface ILuckyLennySwitchGameEvent {
    gameId: LuckyLennyGameId;
    totalWinAmount?: number;
    symbols?: number[];
}

export type ILuckyLennySwitchGameAnimationEvent = boolean;

export type ILuckyLennyTurboEvent = boolean;

export type ICameraTarget = ICoordinates;

export interface IWheelSymbolData {
    fid: number;
    multiplier?: number;
    bonusRound?: 'wheel' | 'gameboard';
}

export type IBackgroundLayerTextureName =
    | 'bonus_background'
    | 'bonus_background_bottom'
    | 'bonus_background_top'
    | 'bg_layer1_top_shadow'
    | 'bg_layer2_top_trees'
    | 'bg_layer3_bottom_trees'
    | 'bg_layer4_bottom_shadow'
    | 'bg_layer5_trees_middle'
    | 'bg_layer6_trees_background';

export type IBackgroundLayerTextureMap = Record<IBackgroundLayerTextureName, Texture>;

export interface IBackgroundLayerGroup {
    spriteName: IBackgroundLayerTextureName;
    horizontal: HorizontalAlign;
    vertical: VerticalAlign;
    offset: ICoordinates;
    scale: number;
    stickToScene?: boolean;
    anchor: ICoordinates;
    layerOrder: LAYER_ORDER;
    parallaxX?: number;
    parallaxY?: number;
}

export type ISpritesTextTextureName = 'text_re_spin' | 'text_total_win';

export type ISpritesTextureMap = Record<ISpritesTextTextureName, Texture>;

// ---------------------------------------------------------------------------
// Shared scale configuration interfaces (used by Lenny and Symbols)
// ---------------------------------------------------------------------------

export interface IScaleValueConfig {
    portrait: number;
    landscape: number;
}

export interface IScaleOrientationConfig<T = IScaleValueConfig> {
    mobile: T;
    desktop: T;
}

// Lucky Lenny specific scale config built on top of shared interfaces
export interface ILennyScaleConfig {
    spine: IScaleOrientationConfig;
    celebration: IScaleOrientationConfig;
    view: IScaleOrientationConfig;
    collisionDistance: IScaleOrientationConfig<number>;
}

// Symbol scale configuration built on the same shared interfaces
export interface ISymbolScaleConfig {
    spine: IScaleOrientationConfig;
    sprite: IScaleOrientationConfig;
    collisionDistance: IScaleOrientationConfig<number>;
}

export type PixiFilters = Filter[] | undefined | null;
