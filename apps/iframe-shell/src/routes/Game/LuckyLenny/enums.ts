export const LennyAnimations = {
    IDLE: 'IdleHovering',
    FLY: 'Flying',
};

export const SymbolAnimations = {
    IDLE: 'Idle',
    ON_SLOT: 'On Slot',
    COLLECT: 'Fruit_Taken_Fx',
};

export enum DeviceType {
    MOBILE = 'mobile',
    DESKTOP = 'desktop',
}

export enum OrientationType {
    PORTRAIT = 'portrait',
    LANDSCAPE = 'landscape',
}

export enum LuckyLennyGameState {
    IDLE,
    PLAYING,
    COLLECTING,
    LEVEL_COMPLETE,
    WHEEL_BONUS,
    AZTEC_BONUS,
}

export enum LuckyLennyEvents {
    spin,
    result,
    lennyCollectComplete, // fruits collection completed, NOT CONSUMED
    endGame, // end game called after "lennyCollectComplete" with short delay if NOT bonus game
    newGame, // game ended and ready for new game to start
    winAmount, // called after each win amount
    winAnimationStart,
    winAnimationFadeOut,
    winAnimationEnd, // new game in case of win
    switchGame,
    switchGameAnimation,
    turbo,
    bonusGameboardClick,
    boardResult,
    bonusGameboardAutoPlay,
    bonusGameboardResult,
    bonusWheelSpin,
    totalWin,
    totalWinAnimationStart,
    totalWinAnimationEnd,
    respin,
    freePlayCompleted,
}

export enum LuckyLennyItems {
    PINEAPPLE,
    BANANA,
    GRAPES,
    CHERRIES,
    APPLE,
    CARROT,
    WHEEL,
    AZTEC,
    RESPIN,
}

export enum SymbolType {
    PINEAPPLE = 'pineapple_symbol',
    BANANA = 'bananas_symbol',
    GRAPES = 'grapes_symbol',
    CHERRIES = 'cherry_symbol',
    APPLE = 'apple_symbol',
    CARROT = 'carrot_symbol',
    WHEEL = 'eldorado_symbol',
    AZTEC = 'aztec_symbol',
    RESPIN = 'respin_symbol',
}

export enum NextAction {
    STANDARD = 'standard',
    WHEEL = 'wheel',
    GAMEBOARD = 'gameboard',
}

export enum BELuckyLennyItems {
    PINEAPPLE,
    BANANA,
    GRAPES,
    CHERRIES,
    APPLE,
    CARROT,
    WHEEL,
    AZTEC,
    RESPIN,
}

export const enum WinTypes {
    BIG = 25,
    MEGA = 150,
    ULTRA = 300,
}

export enum LuckyLennyGameId {
    LennySlotGame,
    LennyWheelGame,
    LennyAztecGame,
}

export const enum PixiEventMode {
    none = 'none',
    passive = 'passive',
    auto = 'auto',
    static = 'static',
    dynamic = 'dynamic',
}

export const enum PixiLineCap {
    butt = 'butt',
    round = 'round',
    square = 'square',
}

export const enum AmountScaleThreshold {
    scaleDown = 1_000_000,
    twiceScaleDown = 10_000_000,
}
