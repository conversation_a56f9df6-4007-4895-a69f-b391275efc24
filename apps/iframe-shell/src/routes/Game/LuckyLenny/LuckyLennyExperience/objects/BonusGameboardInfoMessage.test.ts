import { BonusGameboardInfoMessage } from './BonusGameboardInfoMessage';

describe('BonusGameboardInfoMessage', () => {
    let component: BonusGameboardInfoMessage;

    beforeEach(() => {
        component = new BonusGameboardInfoMessage();
    });

    afterEach(() => {
        component.destroy();
    });

    it('should create successfully', () => {
        expect(component).toBeDefined();
    });

    it('should update text correctly', () => {
        const testText = 'Test Message';
        component.updateText(testText);
        expect(component.view.visible).toBe(false); // Initially hidden
    });

    it('should show and hide correctly', () => {
        component.show();
        expect(component.view.visible).toBe(true);
        
        component.hide();
        expect(component.view.visible).toBe(false);
    });

    it('should enable glow effect', () => {
        component.enableGlow();
        expect(component.view.filters).not.toBeNull();
        expect(component.view.filters?.length).toBe(1);
    });

    it('should disable glow effect', () => {
        component.enableGlow();
        component.disableGlow();
        expect(component.view.filters).toBeNull();
    });

    it('should toggle glow effect', () => {
        // Initially disabled
        expect(component.view.filters).toBeNull();
        
        // Toggle on
        component.toggleGlow();
        expect(component.view.filters).not.toBeNull();
        
        // Toggle off
        component.toggleGlow();
        expect(component.view.filters).toBeNull();
    });

    it('should not enable glow if already enabled', () => {
        component.enableGlow();
        const firstFilters = component.view.filters;
        
        component.enableGlow(); // Try to enable again
        expect(component.view.filters).toBe(firstFilters);
    });

    it('should not disable glow if already disabled', () => {
        expect(component.view.filters).toBeNull();
        
        component.disableGlow(); // Try to disable when already disabled
        expect(component.view.filters).toBeNull();
    });
});
