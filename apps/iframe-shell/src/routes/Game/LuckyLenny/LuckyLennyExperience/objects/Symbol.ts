import { Container, Sprite, Texture } from 'pixi.js';
import { Spine } from '@esotericsoftware/spine-pixi-v8';
import { SpineLoader } from '@bg-services/Pixi/utils/SpineLoader';
import gsap from 'gsap';
import { Orientation, Logger } from '@bg-shared';
import { SymbolType, SymbolAnimations } from '../../enums';
import { getSymbolScale } from '../config/symbolScaleConfig';

export interface ISymbolConfig {
    type: SymbolType;
    normalWeight: number; // Weight for reels 1-9
    lastReelWeight: number; // Weight for reel 10
    payouts: Record<number, number>; // Number of symbols -> multiplier
    texture?: Texture; // Optional texture for fallback
    spine?: Spine; // Optional spine animation
}

export class SymbolObject {
    private sprite: Sprite | null = null;

    public readonly type: SymbolType;

    public isCollected = false;

    public isTargetSymbol = false;

    public view: Container = new Container();

    public spine: Spine | null = null;

    private currentOrientation: Orientation = Orientation.LANDSCAPE;

    private isMobileView = false;

    constructor(parent: Container, type: SymbolType, config: ISymbolConfig, x: number, y: number) {
        this.type = type;
        this.view.position.set(x, y);

        if (config.spine) {
            this.createSpineSymbol(config.spine);
        } else if (config.texture) {
            this.createSpriteSymbol(config.texture);
        } else {
            throw new Error('No texture or spine available for symbol');
        }

        // Apply responsive scaling after creation
        this.updateSpineScale();
        this.updateSpriteSize();

        parent.addChild(this.view);
    }

    private createSpineSymbol(spine: Spine): void {
        // Create a clone of the spine animation using SpineLoader
        this.spine = SpineLoader.getInstance().cloneSpine(spine);

        // Center the spine and apply initial scale
        if (this.spine) {
            this.spine.position.set(0, 0);
            this.updateSpineScale();
            // Add to view
            this.view.addChild(this.spine);
        }
    }

    private createSpriteSymbol(texture: Texture): void {
        // Create sprite with the provided texture
        this.sprite = new Sprite(texture);
        this.sprite.anchor.set(0.5);
        this.updateSpriteSize();
        // Add to view
        this.view.addChild(this.sprite);
    }

    public updateSpineScale(): void {
        if (this.spine) {
            const spineScale = getSymbolScale('spine', this.currentOrientation, this.isMobileView);
            this.spine.scale.set(spineScale);
        }
    }

    public updateSpriteSize(): void {
        if (this.sprite) {
            const spriteSize = getSymbolScale('sprite', this.currentOrientation, this.isMobileView);
            this.sprite.width = spriteSize;
            this.sprite.height = spriteSize;
        }
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        if (orientation !== undefined) {
            this.currentOrientation = orientation;
        }
        if (isMobileView !== undefined) {
            this.isMobileView = isMobileView;
        }

        // Update scales based on new configuration
        this.updateSpineScale();
        this.updateSpriteSize();
    }

    playAnimation(animationName: string, loop = true): void {
        if (this.spine && this.spine.state) {
            this.spine.state.setAnimation(0, animationName, loop);
        }
    }

    collect(): void {
        if (this.isCollected) return;

        this.isCollected = true;
        Logger.log(`Symbol ${this.type} collection started`);

        // Simplified approach: Hide symbol immediately with a brief scale animation
        // This avoids Spine animation completion callback issues
        gsap.to(this.view.scale, {
            x: 0.1,
            y: 0.1,
            duration: 0.3,
            ease: 'power2.out',
            onComplete: () => {
                this.view.visible = false;
                Logger.log(`Symbol ${this.type} collection completed, symbol hidden`);
            },
        });

        // Play collect animation if spine is available (but don't rely on it for hiding)
        if (this.spine) {
            // Clear any existing listeners first to prevent accumulation
            if (this.spine.state) {
                this.spine.state.clearListeners();
            }

            // Play collect animation for visual effect
            this.playAnimation(SymbolAnimations.ON_SLOT, false);

            Logger.log(`Symbol ${this.type} playing collection animation`);
        }
    }

    reset(): void {
        this.isCollected = false;
        this.view.visible = true;
        this.view.scale.set(1);

        // Apply responsive scaling after reset
        this.updateSpineScale();
        this.updateSpriteSize();

        if (this.spine) {
            this.playAnimation(SymbolAnimations.IDLE, true);
        }
    }

    remove(): void {
        // Clean up any resources
        if (this.view.parent) {
            this.view.parent.removeChild(this.view);
        }

        // Clear any spine listeners
        if (this.spine && this.spine.state) {
            // Remove all listeners
            this.spine.state.clearListeners();
        }
    }

    // Update the symbol's position
    public updatePosition(x: number, y: number): void {
        this.view.x = x;
        this.view.y = y;

        if (this.spine) {
            this.spine.position.set(x, y);
        }
    }

    // Get the symbol's bounding box for hit detection
    public getBounds(): { left: number; right: number; top: number; bottom: number } {
        // Use the spine's bounds if available, otherwise create a bounding box
        const size = 60; // Approximate symbol size
        return {
            left: this.view.x - size / 2,
            right: this.view.x + size / 2,
            top: this.view.y - size / 2,
            bottom: this.view.y + size / 2,
        };
    }

    // Get the texture for UI display (if available)
    public getTexture(): Texture | null {
        if (this.sprite && this.sprite.texture) {
            return this.sprite.texture;
        }
        return null;
    }

    public dispose() {
        gsap.killTweensOf(this.spine.scale);
        gsap.killTweensOf(this.view.scale);
        gsap.killTweensOf(this.view);
        this.remove();
        this.view.destroy({ texture: true, children: true });
    }
}
