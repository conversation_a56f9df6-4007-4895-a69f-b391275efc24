import { AnimatedSprite, BlurFilter, Container, Graphics, Sprite } from 'pixi.js';
import gsap from 'gsap';
import { Spine, TrackEntry } from '@esotericsoftware/spine-pixi-v8';
import { Orientation } from '@bg-shared';
import { LENNY_CELEBRATION_HANDS_UP_TIMES } from '../../constants';
import { SymbolObject } from './Symbol';
import { LennyAnimations } from '../../enums';
import { LuckyLennyAssetLoader } from '../utils/LuckyLennyAssetLoader';
import { getLennyCollisionDistance, getLennyScale } from '../config/lennyScaleConfig';

export interface ILennyMovement {
    x: number;
    y: number;
    duration: number;
}

export class Lenny {
    private view: Container = new Container();

    private sprite: Sprite | null = null;

    private spine: Spine | null = null;

    private celebrationSprite: AnimatedSprite = null;

    private placeholder: Graphics;

    private flyingState = false;

    private currentPath: ILennyMovement[] = [];

    private currentPathIndex = 0;

    public size = 40;

    private posX = 0;

    private posY = 0;

    private celebrationLoopCount = 0;

    private currentOrientation: Orientation = Orientation.LANDSCAPE;

    private isMobileView = false;

    private lennyIdleTrack: TrackEntry;

    private maxCelebrationCount = LENNY_CELEBRATION_HANDS_UP_TIMES;

    constructor() {
        this.spine = LuckyLennyAssetLoader.getLennySpine();

        // Disable interactivity on the main container to prevent blocking other elements
        this.view.eventMode = 'none';
        this.view.interactive = false;
        this.view.interactiveChildren = false;

        this.updateScales();
        this.view.addChild(this.spine);

        // Center the spine animation on its anchor point
        this.spine.pivot.set(this.spine.width / 2, this.spine.height / 2);

        this.spine.state.data.setMix(LennyAnimations.IDLE, LennyAnimations.FLY, 0.2);
        this.spine.state.data.setMix(LennyAnimations.FLY, LennyAnimations.IDLE, 0.2);

        this.celebrationSprite = new AnimatedSprite(
            LuckyLennyAssetLoader.getLennyCelebrationSpritesheets(),
        );
        this.celebrationSprite.label = 'LennyCelebration';
        this.celebrationSprite.anchor.set(0.59, 0.59);
        this.celebrationSprite.animationSpeed = 0.4;
        this.celebrationSprite.loop = true;
        this.celebrationSprite.visible = false;
        this.celebrationSprite.onLoop = () => {
            this.celebrationLoopCount++;

            if (this.celebrationLoopCount >= this.maxCelebrationCount) {
                this.celebrationSprite.stop();
                this.celebrationSprite?.onComplete();
                this.celebrationLoopCount = 0;
            }
        };

        this.view.addChild(this.celebrationSprite);
    }

    public get container(): Container {
        return this.view;
    }

    public setCelebrationCount(value: number) {
        this.maxCelebrationCount = value;
    }

    private updateScales(): void {
        if (this.spine) {
            const spineScale = getLennyScale('spine', this.currentOrientation, this.isMobileView);
            this.spine.scale.set(spineScale);
        }

        if (this.celebrationSprite) {
            const celebrationScale = getLennyScale(
                'celebration',
                this.currentOrientation,
                this.isMobileView,
            );
            this.celebrationSprite.scale.set(celebrationScale);
        }
    }

    private updatePosition(x: number, y: number): void {
        this.posX = x;
        this.posY = y;
        this.view.position.set(x, y);
    }

    public setPosition(x: number, y: number): void {
        this.updatePosition(x, y);
    }

    public get x(): number {
        return this.posX;
    }

    public get y(): number {
        return this.posY;
    }

    public get isFlying(): boolean {
        return this.flyingState;
    }

    public flyPath(
        path: ILennyMovement[],
        symbols: SymbolObject[],
        onComplete?: () => void,
        onSymbolCollect?: (symbol: SymbolObject) => void,
    ): void {
        // Force reset if already flying to allow new flight path
        if (this.flyingState) {
            this.reset();
        }

        this.flyingState = true;
        this.currentPath = path;
        this.currentPathIndex = 0;

        if (this.spine) {
            this.spine.state.setAnimation(0, LennyAnimations.FLY, true);
        }

        this.flyToNextPoint(symbols, onComplete, onSymbolCollect);
    }

    private flyToNextPoint(
        symbols: SymbolObject[],
        onComplete?: () => void,
        onSymbolCollect?: (symbol: SymbolObject) => void,
    ): void {
        if (this.currentPathIndex >= this.currentPath.length) {
            this.flyingState = false;

            if (this.spine) {
                this.spine.state.setAnimation(0, LennyAnimations.IDLE, true);
            }

            if (onComplete) onComplete();
            return;
        }

        const point = this.currentPath[this.currentPathIndex];
        this.currentPathIndex++;

        const timeline = gsap.timeline({
            onComplete: () => {
                this.checkSymbolCollision(symbols, onSymbolCollect);
                this.flyToNextPoint(symbols, onComplete, onSymbolCollect);
            },
        });

        // Animate to position
        timeline.to(this.view.position, {
            x: point.x,
            y: point.y,
            duration: point.duration,
            ease: 'power1.inOut',
        });

        timeline.to(
            this.view,
            {
                rotation: Math.random() > 0.5 ? 0.1 : -0.1,
                duration: point.duration / 3,
                yoyo: true,
                repeat: 2,
            },
            0,
        );

        if (this.placeholder || this.sprite) {
            timeline.to(
                this.view.scale,
                {
                    y: 1.1,
                    x: 0.9,
                    duration: 0.2,
                    yoyo: true,
                    repeat: Math.floor(point.duration / 0.4),
                },
                0,
            );
        }

        this.posX = point.x;
        this.posY = point.y;
    }

    private checkSymbolCollision(
        symbols: SymbolObject[],
        onSymbolCollect?: (symbol: SymbolObject) => void,
    ): void {
        const collisionDistance = getLennyCollisionDistance(this.isMobileView);

        symbols.forEach((symbol) => {
            // Only check target symbols that haven't been collected
            if (!symbol.isCollected && symbol.isTargetSymbol) {
                // Get the symbol's world position (accounting for container position)
                const symbolWorldX = symbol.view.x + symbol.view.parent.x;
                const symbolWorldY = symbol.view.y + symbol.view.parent.y;

                const dx = this.posX - symbolWorldX;
                const dy = this.posY - symbolWorldY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < collisionDistance) {
                    symbol.collect();
                    if (onSymbolCollect) onSymbolCollect(symbol);
                }
            }
        });
    }

    public startIdleAnimation(): void {
        if (this.spine) {
            this.lennyIdleTrack = this.spine.state.setAnimation(0, LennyAnimations.IDLE, true);
        }
    }

    public stopIdleAnimation(): void {
        if (!this.spine) {
            gsap.killTweensOf(this.view);
        }
    }

    public reset(): void {
        gsap.killTweensOf(this.view);
        gsap.killTweensOf(this.view.position);
        gsap.killTweensOf(this.view.scale);
        gsap.killTweensOf(this.spine);

        this.flyingState = false;
        this.currentPath = [];
        this.currentPathIndex = 0;

        this.view.rotation = 0;

        // Preserve the correct scale from LENNY_SCALE_CONFIG instead of hardcoding to (1, 1)
        const viewScale = getLennyScale('view', this.currentOrientation, this.isMobileView);
        this.view.scale.set(viewScale);

        if (this.spine) {
            this.lennyIdleTrack = this.spine.state.setAnimation(0, LennyAnimations.IDLE, true);
        }

        this.celebrationSprite.stop();
        this.celebrationSprite.visible = false;
    }

    public dispose(): void {
        gsap.killTweensOf(this.view);
        gsap.killTweensOf(this.view.position);
        gsap.killTweensOf(this.view.scale);
        gsap.killTweensOf(this.spine);
        this.view.destroy();
    }

    public startFlyingAnimation(): void {
        this.flyingState = true;

        if (this.spine) {
            this.spine.state.setAnimation(0, LennyAnimations.FLY, true);
        }
    }

    // todo: better have Lenny's celebration animation and other animation in one Spine skeleton
    // todo: after Image compression Spine image-parts and spritesheet animation has different colors
    public celebrationAnimation(): void {
        this.lennyIdleTrack.trackTime = 1;
        this.lennyIdleTrack.timeScale = 0;

        this.celebrationLoopCount = 0;

        gsap.killTweensOf(this.spine);

        gsap.to(this.spine, {
            alpha: 0,
            duration: 0.1,
        });

        this.celebrationSprite.visible = true;

        this.celebrationSprite.play();

        this.celebrationSprite.onComplete = () => {
            this.celebrationSprite.visible = false;
            this.spine.alpha = 1;
            this.lennyIdleTrack.timeScale = 1;
        };
    }

    /**
     * Apply blur effect to Lenny during fast movement
     */
    public applyBlurEffect(intensity = 2): void {
        // Create horizontal motion blur for speed effect
        const blurFilter = new BlurFilter({
            strengthX: intensity * 2,
            strengthY: 0,
        });
        this.view.filters = [blurFilter];
    }

    /**
     * Remove blur effect from Lenny
     */
    public removeBlurEffect(): void {
        this.view.filters = null;
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        if (orientation !== undefined) {
            this.currentOrientation = orientation;
        }
        if (isMobileView !== undefined) {
            this.isMobileView = isMobileView;
        }

        // Update view scale based on new configuration
        const viewScale = getLennyScale('view', this.currentOrientation, this.isMobileView);

        this.view.scale.set(viewScale);

        // Update spine and celebration scales
        this.updateScales();
    }

    /**
     * Scales down Lenny to 50% of his normal size for bonus mini-games.
     * Calling `reset()` afterwards will restore the size based on configuration.
     */
    public scaleDownForBonusGame(scale = 0.8): void {
        // Use the configured scale for the current orientation/device and halve it
        const viewScale = getLennyScale('view', this.currentOrientation, this.isMobileView);
        this.view.scale.set(viewScale * scale);
    }
}
