import { Graphics, Text } from 'pixi.js';
import { GlowFilter } from 'pixi-filters';
import gsap from 'gsap';
import { AbstractContainer } from './AbstractContainer';

const CONFIG = {
    PADDING_VERTICAL: 5,
    PADDING_HORIZONTAL: 10,
    BORDER_RADIUS: 6,
    GLOW_COLOR: 0xfffb0c, // #fffb0c
    GLOW_DURATION: 1.5, // 1.5 seconds
};

export class BonusGameboardInfoMessage extends AbstractContainer {
    private text: Text;

    private background: Graphics;

    private glowFilter: GlowFilter;

    private glowTween: gsap.core.Tween | null = null;

    private isGlowEnabled = false;

    constructor() {
        super();

        this.create();
    }

    private create(): void {
        this.text = new Text({
            text: '',
            style: {
                fontFamily: 'Montserrat',
                fontSize: 28,
                fontWeight: '800',
                fill: 0xffffff,
                align: 'center',
            },
        });
        this.text.anchor.set(0.5);

        this.background = new Graphics();

        // Initialize glow filter
        this.glowFilter = new GlowFilter({
            color: CONFIG.GLOW_COLOR,
            outerStrength: 0, // Start with no glow
            innerStrength: 0,
            distance: 10,
            quality: 0.5,
        });

        this.addChild(this.background, this.text);
        this.view.pivot.y = 445;
    }

    public updateText(text: string): void {
        this.text.text = text;
        this.handleResize();
    }

    public show(): void {
        this.view.visible = true;
    }

    public hide(): void {
        this.view.visible = false;
    }

    public enableGlow(): void {
        if (this.isGlowEnabled) {
            return;
        }

        this.isGlowEnabled = true;
        this.view.filters = [this.glowFilter];
        this.startGlowAnimation();
    }

    public disableGlow(): void {
        if (!this.isGlowEnabled) {
            return;
        }

        this.isGlowEnabled = false;
        this.stopGlowAnimation();
        this.view.filters = null;
    }

    public toggleGlow(): void {
        if (this.isGlowEnabled) {
            this.disableGlow();
        } else {
            this.enableGlow();
        }
    }

    public handleResize(): void {
        this.background
            .clear()
            .roundRect(
                0,
                0,
                this.text.width + CONFIG.PADDING_HORIZONTAL * 2,
                this.text.height + CONFIG.PADDING_VERTICAL * 2,
                CONFIG.BORDER_RADIUS,
            )
            .fill({ color: 0x1b1b1b, alpha: 0.6 });

        this.text.x = this.background.width * 0.5;
        this.text.y = this.background.height * 0.5;

        this.view.pivot.x = this.background.width * 0.5;
    }

    private startGlowAnimation(): void {
        this.stopGlowAnimation();

        // Create pulsing animation with GSAP
        this.glowTween = gsap.to(this.glowFilter, {
            outerStrength: 2,
            innerStrength: 1,
            duration: CONFIG.GLOW_DURATION,
            ease: 'power2.inOut',
            yoyo: true,
            repeat: -1, // Infinite repeat
        });
    }

    private stopGlowAnimation(): void {
        if (this.glowTween) {
            this.glowTween.kill();
            this.glowTween = null;
        }

        // Reset glow filter values
        this.glowFilter.outerStrength = 0;
        this.glowFilter.innerStrength = 0;
    }

    public override destroy(): void {
        // Clean up glow animation before destroying
        this.stopGlowAnimation();
        super.destroy();
    }
}
