import { Graphics, Text } from 'pixi.js';
import { DropShadowFilter } from 'pixi-filters';
import gsap from 'gsap';
import { AbstractContainer } from './AbstractContainer';

const CONFIG = {
    PADDING_VERTICAL: 5,
    PADDING_HORIZONTAL: 10,
    BORDER_RADIUS: 6,
    GLOW_COLOR: 0xfffb0c,
    GLOW_DURATION: 0.5, // s
    TOTAL_DURATION: 5, // s
};

export class BonusGameboardInfoMessage extends AbstractContainer {
    private text: Text;

    private background: Graphics;

    private shadowFilter: DropShadowFilter;

    private glowTween: gsap.core.Tween | null = null;

    private isShadowEnabled = false;

    constructor() {
        super();

        this.create();
    }

    private create(): void {
        this.text = new Text({
            text: '',
            style: {
                fontFamily: 'Montserrat',
                fontSize: 28,
                fontWeight: '800',
                fill: 0xffffff,
                align: 'center',
            },
        });
        this.text.anchor.set(0.5);

        this.background = new Graphics();

        // Initialize shadow filter
        this.shadowFilter = new DropShadowFilter({
            color: CONFIG.GLOW_COLOR,
            alpha: 0, // Start with no shadow
            blur: 4,
            offset: { x: 0, y: 0 },
            quality: 4,
        });

        this.addChild(this.background, this.text);
        this.view.pivot.y = 445;
    }

    public updateText(text: string): void {
        this.text.text = text;
        this.handleResize();
    }

    public show(): void {
        this.view.visible = true;
    }

    public hide(): void {
        this.view.visible = false;
    }

    public enableGlow(): void {
        if (this.isShadowEnabled) {
            return;
        }

        this.isShadowEnabled = true;
        this.background.filters = [this.shadowFilter, this.shadowFilter, this.shadowFilter];
        this.startShadowAnimation();
    }

    public disableGlow(): void {
        if (!this.isShadowEnabled) {
            return;
        }

        this.isShadowEnabled = false;
        this.stopShadowAnimation();
        this.background.filters = null;
    }

    public handleResize(): void {
        this.background
            .clear()
            .roundRect(
                0,
                0,
                this.text.width + CONFIG.PADDING_HORIZONTAL * 2,
                this.text.height + CONFIG.PADDING_VERTICAL * 2,
                CONFIG.BORDER_RADIUS,
            )
            .fill({ color: 0x1b1b1b, alpha: 0.6 });

        this.text.x = this.background.width * 0.5;
        this.text.y = this.background.height * 0.5;

        this.view.pivot.x = this.background.width * 0.5;
    }

    private startShadowAnimation(): void {
        this.stopShadowAnimation();

        const animationObj = { progress: 0 };
        let loop = 0;
        const start = Date.now();

        this.glowTween?.kill();
        this.glowTween = gsap.to(ani{
            // alpha: 1,
            // blur: 5,
            progress: 1,
            duration: CONFIG.GLOW_DURATION,
            ease: 'power2.out',
            yoyo: true,
            repeat: 9,
            onUpdate: () => {
                this.shadowFilter.alpha = animationObj.progress;
                this.background.alpha = Math.min(0.6 + animationObj.progress * 0.4, 1);
                console.log(
                    `shadowAlpha=${this.shadowFilter.alpha} backgroundAlpha=${this.background.alpha}`,
                );
            },
            onRepeat: () => {
                loop++;
                console.log(`loop=${loop} time=${(Date.now() - start) / 1000}`);
            },
            onComplete: () => {
                console.log(`total time=${(Date.now() - start) / 1000}`);
            },
        }).to(animationObj, {
            progress: 1,
            duration: CONFIG.GLOW_DURATION,
            ease: 'power2.out',
        });
    }

    private stopShadowAnimation(): void {
        if (this.glowTween) {
            this.glowTween.kill();
            this.glowTween = null;
        }

        // Reset shadow filter values
        this.shadowFilter.alpha = 0;
        // this.shadowFilter.blur = 5;
    }

    public override destroy(): void {
        // Clean up shadow animation before destroying
        this.stopShadowAnimation();
        super.destroy();
    }
}
