export class AbstractGrid<TGridValue> {
    public readonly width: number;

    public readonly height: number;

    private readonly line: TGridValue[];

    constructor(width: number, height: number, emptyValue: TGridValue) {
        this.width = width;
        this.height = height;
        this.line = new Array(this.width * this.height).fill(emptyValue);
    }

    public get length() {
        return this.line.length;
    }

    public setValueByIndex(value: TGridValue, i: number) {
        if (i < this.line.length) {
            this.line[i] = value;
        }
    }

    public setValue(x: number, y: number, value: TGridValue) {
        this.line[this.getIndex(x, y)] = value;
    }

    public getValue(x: number, y?: number) {
        if (typeof y === 'number') {
            return this.line[this.getIndex(x, y)];
        }

        return this.line[x];
    }

    public forEach(callback: (value: TGridValue, index: number) => void) {
        this.line.forEach(callback);
    }

    public getXY(index: number): [number, number] {
        return [index % this.width, Math.floor(index / this.width)];
    }

    private getIndex(x: number, y: number) {
        return y * this.width + x;
    }
}
