import { type Sprite, Texture } from 'pixi.js';
import gsap from 'gsap';
import { LuckyLennySound, luckyLennySoundsService } from '../../../business/services/Sounds';
import { PixiEventMode } from '../../../enums';
import { Explosion } from '../Explosion';
import { IBonusBoardGameTextureMap, IBonusBoardGameTextureName } from './interfaces';
import { AbstractContainer } from '../AbstractContainer';

interface ITileOptions<TValue> {
    texture: IBonusBoardGameTextureMap;
    animationTextures: Texture[];
    value: TValue;
    x: number;
    y: number;
    index: number;
}

const MEDIUM_VALUE_THRESHOLD = 11;
const HIGH_VALUE_THRESHOLD = 19;

const ADJUSTED_GLOW_SIZE_AVOID_OVERLAP = 192;

const enum zIndex {
    tile,
    glow,
    explosion,
}

export class ExplosionTile<TTileValue> extends AbstractContainer {
    public readonly tileX: number;

    public readonly tileY: number;

    public readonly index: number;

    public value: TTileValue;

    private readonly textureAnimation: Texture[];

    private readonly glowTexture: Texture;

    private readonly tileSprite: Sprite;

    // Store full texture atlas reference so we can swap textures when the tile
    // value becomes known after an API response.
    private readonly atlas: IBonusBoardGameTextureMap;

    private glowSprite: Sprite;

    private readonly onClickCallbacks: ICallback<ExplosionTile<TTileValue>>[] = [];

    private isGlowed = false;

    private isOpened = false;

    private pendingOverlay: Sprite | null = null;

    constructor({ texture, animationTextures, value, x, y, index }: ITileOptions<TTileValue>) {
        const textureValueName = `bonus_board_tileValue${value}` as IBonusBoardGameTextureName;
        const valueTexture =
            textureValueName in texture ? texture[textureValueName] : Texture.EMPTY;

        super();

        this.atlas = texture;

        const sprite = this.createSprite(this.getTileTextureByValue(value));
        const spriteValue = this.createSprite(valueTexture);

        sprite.addChild(spriteValue);

        sprite.alpha = 0;
        sprite.zIndex = zIndex.tile;

        this.addChild(sprite);

        this.tileSprite = sprite;
        this.index = index;
        this.tileX = x;
        this.tileY = y;
        this.value = value;
        this.visible = false;

        this.textureAnimation = animationTextures;
        this.glowTexture = texture.bonus_board_tileGlow;

        this.addClickHandler();
    }

    /**
     * Update the numerical multiplier assigned to this tile **before** it is
     * revealed.  This is needed because the backend only returns the actual
     * value after the player clicks – initially every tile is treated as
     * empty (-1).
     */
    public setValue(newValue: TTileValue): void {
        this.value = newValue;

        const newTileTexture = this.getTileTextureByValue(newValue);

        const textureValueName = `bonus_board_tileValue${newValue}` as IBonusBoardGameTextureName;
        const valueTexture =
            textureValueName in this.atlas ? this.atlas[textureValueName] : Texture.EMPTY;

        // Replace textures on existing sprite
        this.tileSprite.texture = newTileTexture;
        this.tileSprite.removeChildren();

        const spriteValue = this.createSprite(valueTexture);
        this.tileSprite.addChild(spriteValue);
    }

    /**
     * Display / hide a simple pending animation while waiting for backend
     * response.  Implementation: semi-transparent overlay that pulses.
     */
    public setPending(isPending: boolean): void {
        if (isPending) {
            if (this.pendingOverlay) return;

            this.pendingOverlay = this.createSprite(this.glowTexture);
            this.pendingOverlay.alpha = 0.5;
            this.addChild(this.pendingOverlay);

            gsap.to(this.pendingOverlay, {
                alpha: 0.1,
                yoyo: true,
                repeat: -1,
                duration: 0.5,
            });
        } else if (this.pendingOverlay) {
            gsap.killTweensOf(this.pendingOverlay);
            this.removeChild(this.pendingOverlay);
            this.pendingOverlay.destroy();
            this.pendingOverlay = null;
        }
    }

    public get sprite() {
        return this.tileSprite;
    }

    public get glowed() {
        return this.isGlowed;
    }

    public get opened() {
        return this.isOpened;
    }

    public glow() {
        this.isGlowed = true;
        this.glowSprite = this.createSprite(this.glowTexture, {
            width: ADJUSTED_GLOW_SIZE_AVOID_OVERLAP,
            height: ADJUSTED_GLOW_SIZE_AVOID_OVERLAP,
        });
        this.glowSprite.alpha = 0;
        this.glowSprite.zIndex = zIndex.glow;
        this.addChild(this.glowSprite);

        gsap.to(this.glowSprite, {
            alpha: 1,
            duration: 0.25,
        });
    }

    public forceExplosion() {
        this.onClickCallbacks.length = 0;
        this.explosion(0xff4f0f);
        luckyLennySoundsService.play(LuckyLennySound.CellReveal);
    }

    public onClick(callback: (tile: ExplosionTile<TTileValue>) => void) {
        this.onClickCallbacks.push(callback);
    }

    public triggerClick() {
        if (this.isOpened) {
            return;
        }

        this.onClickCallbacks.forEach((cb) => cb(this));
        luckyLennySoundsService.play(LuckyLennySound.CellClick);
    }

    public reset() {
        this.isOpened = false;
        this.isGlowed = false;
        this.tileSprite.alpha = 0;
        this.visible = false;

        // Remove any existing glow sprite
        if (this.glowSprite) {
            this.removeChild(this.glowSprite);
            this.glowSprite.destroy();
            this.glowSprite = null;
        }

        // Remove any pending overlay
        if (this.pendingOverlay) {
            gsap.killTweensOf(this.pendingOverlay);
            this.removeChild(this.pendingOverlay);
            this.pendingOverlay.destroy();
            this.pendingOverlay = null;
        }

        // Re-enable click handler
        this.addClickHandler();
    }

    public destroy() {
        this.onClickCallbacks.length = 0;
        this.tileSprite.children.forEach((spriteValue) => spriteValue.destroy());
        this.tileSprite.removeChildren();
        this.tileSprite.destroy();
        super.destroy();
    }

    private getTileTextureByValue(value: TTileValue) {
        const castedValue = Number(value);

        if (castedValue > 0) {
            if (castedValue >= HIGH_VALUE_THRESHOLD) {
                return this.atlas.bonus_board_tile3;
            }

            if (castedValue >= MEDIUM_VALUE_THRESHOLD) {
                return this.atlas.bonus_board_tile2;
            }

            return this.atlas.bonus_board_tile1;
        }

        return this.atlas.bonus_board_tileEmpty;
    }

    private explosion(tint?: number) {
        if (this.isOpened) {
            return;
        }

        this.removeClickHandler();
        this.isOpened = true;

        // Pass a copy to protect the original array from any accidental mutations
        const explosion = new Explosion([...this.textureAnimation]);

        explosion.view.zIndex = zIndex.explosion;

        if (tint) {
            explosion.view.tint = tint;
        }

        this.addChild(explosion.view);

        explosion.play();

        explosion.view.onComplete = () => {
            explosion.destroy();
            this.onClickCallbacks.forEach((cb) => cb(this));
        };

        setTimeout(() => {
            this.tileSprite.alpha = 1;
        }, 300);
    }

    public addClickHandler() {
        this.tileSprite.interactive = true;
        this.tileSprite.eventMode = PixiEventMode.dynamic;
        this.tileSprite.cursor = 'pointer';
        this.tileSprite.on('pointerdown', () => this.triggerClick());
    }

    public removeClickHandler() {
        this.tileSprite.interactive = false;
        this.tileSprite.eventMode = PixiEventMode.none;
        this.tileSprite.cursor = 'default';
        this.tileSprite.off('pointerdown');
    }
}
