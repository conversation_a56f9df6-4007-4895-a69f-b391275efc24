import type { Texture } from 'pixi.js';
import { Logger } from '@bg-shared';
import { PixiEventMode } from '../../../enums';
import { IBoardGameMultipliers, type IBonusBoardGameTextureMap } from './interfaces';
import { AbstractContainer } from '../AbstractContainer';
import { BoardGrid } from './BoardGrid';
import { BoardBasic } from './BoardBasic';
import { LuckyLennyAssetLoader } from '../../utils/LuckyLennyAssetLoader';
import { ExplosionTile } from './ExplosionTile';

export class LennyBoard extends AbstractContainer {
    private readonly texture: IBonusBoardGameTextureMap;

    private readonly animationTexture: Texture[];

    public readonly board: BoardBasic;

    constructor() {
        const texture = LuckyLennyAssetLoader.getBonusBoardGameTextures();

        const board = new BoardBasic({
            textures: {
                border: texture.bonus_board_border,
                activeField: texture.bonus_board_active,
                inactiveField: texture.bonus_board_inactive,
            },
        });

        super(board.view);

        this.board = board;
        this.texture = texture;
        this.animationTexture = LuckyLennyAssetLoader.getTileExplosionTextures();

        if (!this.animationTexture || this.animationTexture.length === 0) {
            Logger.error(
                'LennyBoard: Explosion textures missing or empty. The animated sprite may not play correctly.',
            );
        } else {
            Logger.log(
                `LennyBoard: Loaded ${this.animationTexture.length} explosion textures`,
                'lenny',
            );
        }

        this.view.eventMode = PixiEventMode.passive;
    }

    public create() {
        const ADJUST_INACTIVE_SPRITE_ACCORDING_GRID_SPRITE = -6;

        this.board.create();
        this.board.inactiveBoard.y += ADJUST_INACTIVE_SPRITE_ACCORDING_GRID_SPRITE;
    }

    public addTiles(
        grid: BoardGrid,
        onClick?: (tile: ExplosionTile<IBoardGameMultipliers>) => void,
    ) {
        const ADJUST_TILE_Y_POSITION_ACCORDING_GRID_SPRITE = -5;

        return this.board.addTiles<IBoardGameMultipliers, ExplosionTile<IBoardGameMultipliers>>(
            grid,
            (options) => {
                const tile = new ExplosionTile<IBoardGameMultipliers>({
                    ...options,
                    texture: this.texture,
                    animationTextures: this.animationTexture,
                });

                if (onClick) {
                    tile.onClick(() => onClick(tile));
                }

                return tile;
            },
            (tile) => {
                tile.y += ADJUST_TILE_Y_POSITION_ACCORDING_GRID_SPRITE;
            },
        );
    }

    public start() {
        this.board.inactiveBoard.visible = false;
    }

    public destroy() {
        this.board.destroy();
        super.destroy();
    }
}
