import { Container } from 'pixi.js';
import TickerService from '@bg-services/ticker.service';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { Orientation } from '@bg-shared';
import {
    IExperienceOptions,
    ILuckyLennyEventManager,
    ILuckyLennyResultItems,
    IPlayResponse,
} from '../../interfaces';
import { gameConfigSelectors } from '../../business/stores/gameConfig.selectors';
import { LuckyLennyAssetLoader } from '../utils/LuckyLennyAssetLoader';
import { NextAction } from '../../enums';
import { LuckyLennySceneManager } from '../managers/LuckyLennySceneManager';

class LennyExperienceBase {
    protected readonly canvas: IExperienceOptions['canvas'];

    protected viewport: PixiViewport;

    protected eventManager: ILuckyLennyEventManager;

    protected width: number;

    protected height: number;

    protected isReady = false;

    isPaused = false;

    private stats: Stats;

    isDebugMode = false;

    protected onLoadComplete?: () => void;

    private stage: Container = new Container();

    protected lennySceneManager: LuckyLennySceneManager;

    private orientation: Orientation;

    private isMobileView: boolean;

    protected totalWinAmount: number;

    protected multiplier: number;

    protected result: number[];

    protected nextAction: NextAction;

    protected eventUnsubscribers: (() => void)[] = [];

    constructor({
        canvas,
        eventManager,
        enableDebug = false,
        onLoadComplete,
        width,
        height,
    }: IExperienceOptions) {
        this.canvas = canvas;
        this.eventManager = eventManager;
        this.width = width;
        this.height = height;
        this.isDebugMode = enableDebug;
        this.onLoadComplete = onLoadComplete;
    }

    protected initialize = async (): Promise<void> => {
        this.viewport.add(this.stage);

        this.lennySceneManager = new LuckyLennySceneManager(
            this.stage,
            this.viewport,
            this.eventManager,
        );
        // this.lennySceneManager.setDebugMode(this.isDebugMode);
        this.lennySceneManager.initialize();
    };

    public handleResize = (isMobileView: boolean, width: number, height: number): void => {
        if (!this.viewport) return;

        const isLandscape = width > height;

        this.orientation = isLandscape ? Orientation.LANDSCAPE : Orientation.PORTRAIT;
        this.isMobileView = isMobileView;

        this.viewport.width = width;
        this.viewport.height = height;
        this.viewport.resize();

        this.lennySceneManager?.handleResize(this.orientation, this.isMobileView);
    };

    protected readonly update = (): void => {
        if (!this.isReady || this.isPaused) {
            return;
        }

        this.lennySceneManager?.update();
        this.viewport.update();
    };

    public dispose = (): void => {
        this.isReady = false;

        TickerService.removeListener(this.update);

        this.stage?.destroy({ children: true, texture: true });

        LuckyLennyAssetLoader.getInstance().unloadAllAssets();

        if (this.stats?.dom.parentNode) {
            this.stats.dom.parentNode.removeChild(this.stats.dom);
        }
        this.stats = null;

        this.viewport?.dispose();

        this.viewport = null;

        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        this.eventManager = null;

        this.lennySceneManager.dispose();
    };

    public onSpin = (response: IPlayResponse): void => {
        const { lastRoundResult } = response;
        const symbols = (lastRoundResult?.symbols ?? []) as ILuckyLennyResultItems;
        const nextActionFromResponse = response.nextAction as NextAction | undefined;
        const nextActionInRound =
            (lastRoundResult?.bonusRounds?.[0] as NextAction | undefined) ??
            nextActionFromResponse ??
            NextAction.STANDARD;
        // todo: need BE fix, total win must be 0 if we have not bonus games
        this.totalWinAmount = gameConfigSelectors.getTotalAmountWon(response);
        this.multiplier = lastRoundResult?.totalMultiplier ?? 0;
        this.nextAction = nextActionInRound;
        this.result = symbols;
        this.lennySceneManager.onSpin(response);
    };

    public onStartFromScratch = (symbols: number[]): void => {
        this.lennySceneManager.onStartFromScratch(symbols);
    };

    public onBonusGameboardClickResponse = (response: IPlayResponse, symbolIndex: number): void => {
        this.lennySceneManager.onBonusGameboardClickResponse(response, symbolIndex);
    };
}

export default LennyExperienceBase;
