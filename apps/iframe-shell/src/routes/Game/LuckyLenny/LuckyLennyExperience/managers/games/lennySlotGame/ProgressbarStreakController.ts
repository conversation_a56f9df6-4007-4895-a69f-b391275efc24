import type { IActiveStreak, ICellItem, StreakLimits } from '../../../interfaces/progressbar';
import type { Progressbar } from '../../../objects/progressbar/Progressbar';

interface IProgressbarControllerOptions<TValue extends string> {
    streakLimits: StreakLimits<TValue>;
}

/**
    We must have at least 2 items for start streak logic or specific one-item-streak
    - 1st exception is value min=1 and max=1 - streak from ONE element (special symbols [Lucky Lenny RE-SPIN])
    - 2nd exception is value min=0 and max=0 - no any streak logic

    See ProgressbarStreakController.optionValidation()
 */
export class ProgressbarStreakController<
    TValue extends string,
    TCellItem extends ICellItem = ICellItem,
> {
    private readonly progressbar: Progressbar<TCellItem>;

    private readonly itemValues: TValue[] = [];

    private readonly options: IProgressbarControllerOptions<TValue>;

    private index = 0;

    private streakLength = 0;

    private activeStreakValue: IActiveStreak<TValue>;

    private readonly streakStack: IActiveStreak<TValue>[] = [];

    private streakMaxItems: Partial<Record<TValue, boolean>> = {};

    constructor(
        progressbar: Progressbar<TCellItem>,
        options: IProgressbarControllerOptions<TValue>,
    ) {
        this.progressbar = progressbar;
        this.options = { ...options };
        this.optionValidation();
    }

    private optionValidation() {
        Object.values<{ min: number; max: number }>(this.options.streakLimits).forEach(
            ({ min, max }) => {
                if ((min === 1 && max === 1) || (min === 0 && max === 0)) {
                    return;
                }

                if (min > max) {
                    throw new Error(
                        `Min value must be less or equal max, got min ${min}, max ${max}`,
                    );
                }

                if (min < 2) {
                    throw new Error(
                        `Min streak count must be more than 1, got min ${min}, max ${max}`,
                    );
                }
            },
        );
    }

    public set vertical(isVertical: boolean) {
        this.progressbar.updateOrientation(isVertical);

        if (isVertical) {
            this.progressbar.view.position.x += 100;
            this.progressbar.view.position.y -= 180;
        }
    }

    public get activeStreak() {
        return this.activeStreakValue;
    }

    public create() {
        this.progressbar.create();
        this.vertical = this.progressbar.vertical;
    }

    public reset() {
        this.index = 0;
        this.streakStack.length = 0;
        this.itemValues.length = 0;
        this.streakLength = 0;
        this.streakMaxItems = {};
        this.progressbar.reset();
    }

    public add(value: TValue, itemView: TCellItem) {
        const { index } = this;

        if (index < this.progressbar.length) {
            this.addItem(value, itemView, index).streakItemsAnimation(index);
        }

        return itemView;
    }

    public destroy() {
        this.itemValues.length = 0;
        this.streakStack.length = 0;
        this.streakLength = 0;
        this.activeStreakValue = undefined;
        this.streakMaxItems = {};
        this.progressbar.destroy();
    }

    private getItemValue(index: number) {
        return this.itemValues[index];
    }

    private streakItemsAnimation(index: number) {
        const itemValue = this.getItemValue(index);

        if (this.streakMaxItems[itemValue]) {
            this.removeIncompleteStreakView(index - 1);
            this.clearStreak(index);
            return this;
        }

        const { min, max } = this.options.streakLimits[itemValue];

        if (max === 1) {
            this.showStreakByIndex(index).progressbar.upscale(index);
            this.activeStreakValue = {
                id: this.getStreakId(),
                index,
                value: itemValue,
                length: 1,
            };
            this.streakStack.push(this.activeStreakValue);
            this.removeIncompleteStreakView(index - 1);
            this.streakLength = 0;

            return this;
        }

        if (this.streakLength < 2) {
            return this;
        }

        if (index - this.streakLength + min > this.progressbar.length - 1) {
            this.clearStreak(index);
            return this;
        }

        const isTheSamePrev = itemValue === this.getItemValue(index - 1);

        if (this.streakLength > 1 && isTheSamePrev && max > 1) {
            this.showStreakByIndex(index);

            if (this.streakLength === 2) {
                this.showStreakByIndex(index - 1);
            }

            if (this.streakLength === min) {
                for (let j = index; j > index - min; j--) {
                    this.progressbar.upscale(j);
                }
            } else if (this.streakLength > min) {
                this.progressbar.upscale(index);
            }

            if (index + 1 < this.progressbar.length && this.streakLength < max) {
                this.progressbar.highlight(index + 1);
            }

            if (this.streakLength >= min) {
                const id = this.activeStreakValue ? this.activeStreakValue.id : this.getStreakId();

                this.activeStreakValue = {
                    id,
                    index,
                    value: itemValue,
                    length: this.streakLength < max ? this.streakLength : max,
                };

                if (this.streakLength === max) {
                    this.streakLength = 0;
                    this.streakMaxItems[itemValue] = true;
                    this.streakStack.push(this.activeStreakValue);
                }
            }

            return this;
        }

        if (this.activeStreakValue) {
            this.streakStack.push(this.activeStreakValue);
        }

        this.removeIncompleteStreakView(index - 1);

        this.activeStreakValue = undefined;
        this.streakLength = max > 0 ? 1 : 0;

        this.progressbar.normal(index);

        return this;
    }

    private clearStreak(index: number) {
        this.progressbar.normal(index);
        this.streakLength = 0;
    }

    private addItem(value: TValue, itemView: TCellItem, index: number) {
        this.progressbar.setItem(itemView, index);
        this.itemValues[index] = value;

        if (this.streakLength === 0) {
            this.activeStreakValue = undefined;
        }

        this.streakLength++;
        this.index++;

        return this;
    }

    private showStreakByIndex(index: number) {
        this.progressbar.highlight(index).streak(index);

        return this;
    }

    private removeIncompleteStreakView(index: number) {
        if (index < 1) {
            return;
        }

        const itemValue = this.getItemValue(index);
        const { min } = this.options.streakLimits[itemValue];

        if (this.streakLength - 1 >= min) {
            return;
        }

        for (let j = index; j > index - this.streakLength + 1; j--) {
            this.progressbar.normal(j);
        }
    }

    private getStreakId = (() => {
        let i = 1;

        return () => i++;
    })();
}
