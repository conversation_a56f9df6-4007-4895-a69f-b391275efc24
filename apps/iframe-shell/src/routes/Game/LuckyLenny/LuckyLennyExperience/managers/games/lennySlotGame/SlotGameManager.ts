import { ICoordinates, isPortrait, Logger, Orientation } from '@bg-shared';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { random } from 'lodash-unified';
import { Container } from 'pixi.js';
import gsap from 'gsap';
import { EventManager } from '@bg-services';
import { ILayoutOptions } from '@bg-services/Pixi/LayoutManagerV2';
import { gameConfigSelectors } from '../../../../business/stores/gameConfig.selectors';
import { RESULTS_LENGTH, TURBO_MODE_SPEED_FACTOR } from '../../../../constants';
import type { ILuckyLennySwitchGameEvent, IPlayResponse } from '../../../../interfaces';
import { luckyLennySoundsService } from '../../../../business/services/Sounds';
import { Multiplier } from '../../../objects/Multiplier';
import type { IGameManager } from '../../../interfaces/gameManager';
import { ArrangementVariant, SymbolManager } from '../../symbols/SymbolManager';
import { LennyController } from '../../LennyController';
import {
    LuckyLennyEvents,
    LuckyLennyGameId,
    LuckyLennyGameState,
    NextAction,
    SymbolType,
} from '../../../../enums';
import { LuckyLennyAssetLoader } from '../../../utils/LuckyLennyAssetLoader';
import { ISymbolConfig } from '../../../objects/Symbol';
import { GameManagerBase } from '../GameManagerBase';
import { InfiniteBackground } from '../../../objects/background/InfiniteBackground';
import { LAYER_ORDER, SymbolConfigs } from '../../../config';
import { ProgressbarItemSpineAlignment } from '../../../objects/progressbar/ProgressbarItemSpineAlignment';
import { ProgressbarStreakController } from './ProgressbarStreakController';
import { Progressbar } from '../../../objects/progressbar/Progressbar';
import { Cell } from '../../../objects/progressbar/Cell';
import { STREAK_LIMITS } from '../../../config/streakLimits';
import { MultiplierController } from './MultiplierController';
import { getSymbolTint } from '../../../utils/getSymbolTint';
import { Explosion } from '../../../objects/Explosion';
import { featureFlags } from '../../../config/featureFlags';
import { ToSymbolType } from '../../../utils/utils';

const SLOT_GAME_OFFSET_Y = {
    desktop: 100,
    mobile: 0,
};

const SHOW_SYMBOLS_DURATION = 0.5;
const TURBO_SHOW_SYMBOLS_DURATION = 0.2;

const RESET_Y_PARALLAX_DURATION = 0.8;
const TURBO_RESET_Y_PARALLAX_DURATION = 0.3;

const GAME_CONTAINER_Y_POSITION_DURATION = 0.3;
const TURBO_GAME_CONTAINER_Y_POSITION_DURATION = 0.1;

export class SlotGameManager extends GameManagerBase implements IGameManager {
    protected insideGameContainer: Container = new Container();

    private selectedSymbols: SymbolType[] = [];

    protected symbolManager: SymbolManager;

    private movementSpeed = 40;

    private cameraSmoothFactor = 0.5;

    private onContainerResetCallback?: () => void;

    protected symbolConfigs: ISymbolConfig[];

    protected infiniteBackground: InfiniteBackground;

    private progressbarController: ProgressbarStreakController<
        SymbolType,
        ProgressbarItemSpineAlignment
    >;

    private multiplierController: MultiplierController<SymbolType, ProgressbarItemSpineAlignment>;

    // Track order of symbol positions for collection callbacks
    private targetSymbolPositions: Array<ICoordinates> = [];

    private progressbar: Progressbar<ProgressbarItemSpineAlignment>;

    private progressbarWrapper = new Container();

    // Store unsubscribe functions for event listeners
    private eventUnsubscribers: (() => void)[] = [];

    /**
     * Timeout id for the delayed `showSymbols` after symbol grid creation.
     * Needed so we can cancel it during `dispose()` to avoid calling into disposed objects.
     */
    private showSymbolsTimeoutId?: number;

    /**
     * Flag to prevent updates during disposal
     */
    private isDisposing = false;

    constructor(stage: Container, viewport: PixiViewport, eventManager: EventManager) {
        super(stage, viewport, eventManager);

        this.gameId = LuckyLennyGameId.LennySlotGame;

        this.stage.addChild(this.activeGameContainer);
        this.activeGameContainer.zIndex = LAYER_ORDER.MAIN_GAME;
        this.activeGameContainer.addChild(this.insideGameContainer);
        // Allow manual zIndex ordering
        this.insideGameContainer.sortableChildren = true;
        this.setupEvents();

        this.infiniteBackground = new InfiniteBackground(this.stage, this.viewport);
        this.viewport.layoutManager.register(this.activeGameContainer, {
            horizontal: 'center',
            vertical: 'center',
            offsetY: SLOT_GAME_OFFSET_Y[this.isMobileView ? 'mobile' : 'desktop'],
        });
    }

    public blur(enable: boolean) {
        super.blur(enable);

        const filters = enable ? this.blurFilter : undefined;

        this.infiniteBackground.filters = filters;
        this.progressbar.filters = filters;
    }

    public turboModeEnable(isTurbo: boolean) {
        super.turboModeEnable(isTurbo);

        if (this.symbolManager) {
            this.symbolManager.showSymbolDuration = this.isTurboModeEnabled
                ? TURBO_SHOW_SYMBOLS_DURATION
                : SHOW_SYMBOLS_DURATION;
        }
    }

    public registerTopLevelContainer(topLevelContainer: Container) {
        this.viewport.layoutManager.register(topLevelContainer, {
            horizontal: 'center',
            vertical: 'center',
            offsetY: SLOT_GAME_OFFSET_Y[this.isMobileView ? 'mobile' : 'desktop'],
        });
    }

    public setup(symbols: number[]): Promise<void> {
        super.setup(symbols);
        return this.initialize();
    }

    public setProgressSymbolsFromLastRoundResult(): void {
        super.startGame();
        this.progressbarController.reset();
    }

    public setupEvents(): void {
        this.eventUnsubscribers.push(
            // Pause game when win animation starts
            this.eventManager.on(LuckyLennyEvents.winAnimationStart, () => {
                this.pauseGame();
            }),
            // Resume game when win animation ends
            this.eventManager.on(LuckyLennyEvents.winAnimationEnd, () => {
                this.reset();
            }),
        );
    }

    public async initialize(): Promise<void> {
        this.setState(LuckyLennyGameState.IDLE);
        await this.setSymbolConfigs();
        this.symbolManager = new SymbolManager(this.insideGameContainer, this.symbolConfigs);
        this.generateSymbols();
    }

    public setState(state: LuckyLennyGameState): void {
        this.currentState = state;
    }

    public getState(): LuckyLennyGameState {
        return this.currentState;
    }

    public activeLenny(lennyController: LennyController): void {
        Logger.log('SlotGameManager: activeLenny called');
        super.activeLenny(lennyController);
        this.lennyController.addLennyToContainer(this.insideGameContainer);
        this.lennyController.onCollect(this.handleSymbolCollected);
        this.createProgressbar();
    }

    private createProgressbar() {
        const vertical = isPortrait(this.viewport.width, this.viewport.height);

        this.progressbar = new Progressbar<ProgressbarItemSpineAlignment>(
            () =>
                new Cell<ProgressbarItemSpineAlignment>(
                    LuckyLennyAssetLoader.getProgressbarTextures(),
                    {},
                ).create(),
            { width: 120, height: 120, vertical },
        );
        this.progressbarController = new ProgressbarStreakController<
            SymbolType,
            ProgressbarItemSpineAlignment
        >(this.progressbar, { streakLimits: STREAK_LIMITS });
        this.progressbarController.create();

        this.multiplierController = new MultiplierController(
            this.progressbar,
            ({ value, anchor }) => new Multiplier(value, anchor),
        );

        this.progressbarWrapper.zIndex = LAYER_ORDER.COLLECTION_UI;
        this.progressbarWrapper.addChild(this.progressbar.view);

        this.stage.addChild(this.progressbarWrapper);

        this.registerProgressbar(vertical);

        if (this.lastSlotGameResponse && this.lastSlotGameResponse.length) {
            const symbolTypes = ToSymbolType(this.lastSlotGameResponse);
            symbolTypes.forEach((symbol) => this.addSymbolToProgressbar(symbol));
        }
    }

    private registerProgressbar(vertical: boolean) {
        this.viewport.layoutManager.unregister(this.progressbarWrapper);

        const progressbarLayoutOptions: ILayoutOptions = vertical
            ? { horizontal: 'left', stickToScene: true }
            : { vertical: 'top', offsetY: 70, stickToScene: true };

        this.progressbarWrapper.position.set(0, 0);

        // Only set vertical property if controllers are initialized
        if (this.progressbarController) {
            this.progressbarController.vertical = vertical;
        }
        if (this.multiplierController) {
            this.multiplierController.vertical = vertical;
        }

        this.viewport.layoutManager.register(this.progressbarWrapper, progressbarLayoutOptions);
        this.viewport.layoutManager.update();
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        // First, let the base class store the latest mobile / desktop flag
        super.handleResize(orientation, isMobileView);

        this.infiniteBackground.handleResize(this.viewport.width, this.viewport.height);

        // Update layout offsets based on the CURRENT device type
        this.viewport.layoutManager.setOptions(this.activeGameContainer, {
            offsetY: SLOT_GAME_OFFSET_Y[this.isMobileView ? 'mobile' : 'desktop'],
        });

        // Progress-bar may switch between vertical / horizontal depending on orientation
        this.registerProgressbar(orientation === Orientation.PORTRAIT);

        // Propagate resize to game elements
        this.symbolManager?.handleResize(orientation, this.isMobileView);
        this.lennyController?.handleResize(orientation, this.isMobileView);
        this.infiniteBackground?.handleResize(this.viewport.width, this.viewport.height);
    }

    public setOnContainerResetCallback(callback: () => void): void {
        this.onContainerResetCallback = callback;
    }

    public async setSymbolConfigs(): Promise<void> {
        this.symbolConfigs = await LuckyLennyAssetLoader.getSymbolConfigs();
    }

    public updateCamera(): void {
        const lennyPos = this.lennyController.getPosition();

        const targetContainerX = -lennyPos.x;
        const targetContainerY = -lennyPos.y;

        this.insideGameContainer.x +=
            (targetContainerX - this.insideGameContainer.x) * this.cameraSmoothFactor;
        this.insideGameContainer.y +=
            (targetContainerY - this.insideGameContainer.y) * this.cameraSmoothFactor;
    }

    public setArrangementVariant(newVariant?: ArrangementVariant): void {
        this.symbolManager.setArrangementVariant(newVariant);
    }

    private onLennyCollectionComplete = (): void => {
        Logger.log('SlotGameManager: Collection complete, hiding symbols');
        this.hideSymbols();

        const yParallaxDuration = this.isTurboModeEnabled
            ? TURBO_RESET_Y_PARALLAX_DURATION
            : RESET_Y_PARALLAX_DURATION;
        this.infiniteBackground.resetYParallax(yParallaxDuration); // Smooth reset over 0.8 seconds

        const winAmount = gameConfigSelectors.getTotalAmountWon(this.response);

        if (winAmount) {
            this.eventManager.once(LuckyLennyEvents.winAnimationEnd, () => {
                this.doNextGameAction();
            });
            this.eventManager.trigger(LuckyLennyEvents.winAmount, {
                winAmount,
                stake: this.response.gameRoundInfo ? this.response.gameRoundInfo.stake : 1, // TODO: check with BE team if this is correct
                multiplier: this.response.lastRoundResult.totalMultiplier,
                showPlusReSpin: false,
            });
        } else {
            setTimeout(
                () => this.doNextGameAction(),
                300 * (this.isTurboModeEnabled ? TURBO_MODE_SPEED_FACTOR : 1),
            );
        }
    };

    private doNextGameAction() {
        this.pauseGame();

        if (this.nextAction !== NextAction.STANDARD) {
            this.eventManager.trigger<ILuckyLennySwitchGameEvent>(LuckyLennyEvents.switchGame, {
                gameId:
                    this.nextAction === NextAction.WHEEL
                        ? LuckyLennyGameId.LennyWheelGame
                        : LuckyLennyGameId.LennyAztecGame,
            });
        } else {
            this.eventManager.trigger(LuckyLennyEvents.endGame);
        }
    }

    public startLennyCollection = (
        selectedSymbols: SymbolType[],
        symbolPositions: Array<ICoordinates>,
    ): void => {
        this.selectedSymbols = selectedSymbols;

        if (this.selectedSymbols.length === 0) {
            Logger.log('LennyExperience: No symbols selected, cannot start collection');
            return;
        }

        if (symbolPositions.length !== selectedSymbols.length) {
            Logger.log('LennyExperience: Mismatch between symbols and positions count');
            return;
        }

        if (this.getState() === LuckyLennyGameState.IDLE) {
            this.setState(LuckyLennyGameState.PLAYING);
        }

        this.targetSymbolPositions = symbolPositions;

        this.symbolManager.showSymbols();

        // Calculate final landing position
        const symbolColumns = this.symbolManager.getSymbolColumns();
        const rightmostX = symbolColumns.reduce(
            (max, col) => (col.x > max ? col.x : max),
            -Infinity,
        );
        const landingTargetX = rightmostX + 600;
        const landingTargetY = 0; // Ground level

        Logger.log(
            `[COLLECTION] Final landing target: (${landingTargetX.toFixed(
                2,
            )}, ${landingTargetY.toFixed(2)})`,
        );

        Logger.log(
            `[COLLECTION] About to call startFlyingToCollectWithPositions with finalTargetPosition: (${landingTargetX.toFixed(
                2,
            )}, ${landingTargetY.toFixed(2)})`,
        );

        this.lennyController.startFlyingToCollectWithPositions(
            this.selectedSymbols,
            this.targetSymbolPositions,
            this.onLennyCollectionComplete,
            { x: landingTargetX, y: landingTargetY }, // Pass final landing position
        );
    };

    private hideSymbols = (): void => {
        if (this.symbolManager) {
            this.symbolManager.hideSymbols();
            // Only force hide symbols that haven't been collected yet
            this.symbolManager.symbols.forEach((symbol) => {
                if (symbol.view && symbol.view.parent && !symbol.isCollected) {
                    symbol.view.visible = false;
                }
            });

            // Debug: Check if any symbols are still visible
            const visibleSymbols = this.symbolManager.symbols.filter((s) => s.view.visible);
            if (visibleSymbols.length > 0) {
                Logger.warn(
                    `SlotGameManager: ${visibleSymbols.length} symbols still visible after hiding:`,
                    visibleSymbols.map((s) => `${s.type} (collected: ${s.isCollected})`),
                );
            } else {
                Logger.log('SlotGameManager: All symbols successfully hidden');
            }
        }
    };

    public update(): void {
        super.update();

        // Don't update if game is being disposed or lennyController is not available
        if (this.isDisposing || !this.lennyController || !this.insideGameContainer) {
            return;
        }

        // Track previous coordinates for parallax calculation
        const prevX = this.insideGameContainer.x;
        const prevY = this.insideGameContainer.y;

        if (this.getState() === LuckyLennyGameState.PLAYING && !this.isPaused) {
            if (this.lennyController.isLennyFlying()) {
                // Keep centre during collection with vertical movement animation
                const lennyPos = this.lennyController.getPosition();
                if (lennyPos) {
                    const verticalOffset = this.lennyController.getVerticalMovementOffset();

                    this.insideGameContainer.x = -lennyPos.x;
                    this.insideGameContainer.y = -lennyPos.y + verticalOffset;
                }
            } else {
                // Continuous running movement
                const currentPos = this.lennyController.getPosition();
                if (currentPos) {
                    this.lennyController.moveTo(currentPos.x + this.movementSpeed, currentPos.y);
                    this.insideGameContainer.x -= this.movementSpeed;
                }
            }
        }

        const deltaX = this.insideGameContainer.x - prevX;
        const deltaY = this.insideGameContainer.y - prevY;

        if (deltaX !== 0 || deltaY !== 0) {
            this.infiniteBackground?.applyParallaxMovement(deltaX, deltaY);
        }
    }

    private addSymbolToProgressbar(symbol: SymbolType) {
        const spineConfig = LuckyLennyAssetLoader.getSymbolSpine(symbol);

        const item = this.progressbarController.add(
            symbol,
            new ProgressbarItemSpineAlignment(symbol, spineConfig),
        );
        this.multiplierController.updateActiveStreak(this.progressbarController.activeStreak);
        luckyLennySoundsService.handleSymbolCollected(
            symbol,
            this.progressbarController.activeStreak?.length >= STREAK_LIMITS[symbol].min,
        );

        return item;
    }

    public generateSymbols = (arr: SymbolType[] = []): void => {
        this.setArrangementVariant(ArrangementVariant.STRAIGHT);

        // Reset the straight path before generating new symbols
        this.symbolManager.resetStraightPath();

        if (arr.length > 0) {
            this.selectedSymbols = arr;
        } else {
            this.selectedSymbols = new Array(RESULTS_LENGTH)
                .fill(null)
                .map(() => SymbolConfigs[random(0, SymbolConfigs.length - 1)].type);
        }

        this.symbolManager.createSymbolGrid(
            this.selectedSymbols,
            this.lennyController?.getPosition()?.x,
            1, // maxXVariation = 1 for gradual up/down movement from previous position
        );

        // Reset cached target positions – they will be computed right before collection
        this.targetSymbolPositions = [];

        // Show symbols with bounce animation after they're created

        if (this.isTurboModeEnabled) {
            this.symbolManager.showSymbols(0);
        } else {
            this.showSymbolsTimeoutId = setTimeout(() => {
                if (this.symbolManager) {
                    Logger.log('LennyExperience: Calling symbolManager.showSymbols()');
                    this.symbolManager.showSymbols();
                } else {
                    Logger.warn(
                        'LennyExperience: symbolManager is null when trying to show symbols',
                    );
                }
                this.showSymbolsTimeoutId = undefined;
            }, 100); // Small delay to ensure symbols are properly created
        }
    };

    public reset(): void {
        this.selectedSymbols = [];
        this.targetSymbolPositions = [];

        // Stop automatic forward movement only if symbolManager is initialized
        if (this.symbolManager) {
            this.pauseGame();
            // Reset the straight path when resetting the game
            this.symbolManager.resetStraightPath();
        } else {
            Logger.log('SlotGameManager: reset called but symbolManager is not initialized yet');
        }

        // Clear current selection so the next spin can assign fresh targets

        // Switch to idle so external controllers know round finished
        this.setState(LuckyLennyGameState.IDLE);

        this.onContainerResetCallback?.();

        // Generate symbols so the first column is visible on screen
        this.generateSymbols();
    }

    public startGame(): void {
        this.isPaused = false;
        this.setState(LuckyLennyGameState.PLAYING);

        this.triggerCollection();
    }

    public triggerCollection(): void {
        if (
            this.getState() === LuckyLennyGameState.PLAYING &&
            !this.lennyController.isLennyFlying()
        ) {
            const positions = this.symbolManager.getTargetSymbolPositions(this.selectedSymbols);

            // Calculate final landing position
            const symbolColumns = this.symbolManager.getSymbolColumns();
            const rightmostX = symbolColumns.reduce(
                (max, col) => (col.x > max ? col.x : max),
                -Infinity,
            );
            const landingTargetX = rightmostX + 600;
            const landingTargetY = 0; // Ground level

            this.lennyController.startFlyingToCollectWithPositions(
                this.selectedSymbols,
                positions,
                this.onLennyCollectionComplete,
                { x: landingTargetX, y: landingTargetY }, // Pass final landing position
            );
        }
    }

    public pauseGame(): void {
        if (this.isPaused) {
            return;
        }

        this.isPaused = true;
        if (this.symbolManager) {
            this.symbolManager.stopMoving();
        } else {
            Logger.warn(
                'SlotGameManager: pauseGame called but symbolManager is not initialized yet',
            );
        }
    }

    public resumeGame(): void {
        this.isPaused = false;

        this.symbolManager?.startMoving();
    }

    // Toggle pause state
    public togglePause(): void {
        this.isPaused = !this.isPaused;

        if (this.symbolManager) {
            if (this.isPaused) {
                this.symbolManager.stopMoving();
            } else {
                this.symbolManager.startMoving();
            }
        } else {
            Logger.warn(
                'SlotGameManager: togglePause called but symbolManager is not initialized yet',
            );
        }
    }

    /**
     * Handle visual effects and camera adjustments when Lenny collects a symbol.
     */
    private handleSymbolCollected = (symbol: SymbolType, pos: ICoordinates): void => {
        if (!this.symbolManager) {
            Logger.log('SlotGameManager: handleSymbolCollected: symbolManager not found');
            return;
        }

        // Determine collected symbol reference before spawning explosion
        let collectedSymbol = this.symbolManager.findSymbolByCoordinates(pos.x, pos.y);

        if (!collectedSymbol) {
            const candidates = this.symbolManager.symbols.filter(
                (s) => s.isTargetSymbol && !s.isCollected && s.type === symbol,
            );
            if (candidates.length) {
                candidates.sort((a, b) => {
                    const [dx1, dy1] = [a.view.x - pos.x, a.view.y - pos.y];
                    const [dx2, dy2] = [b.view.x - pos.x, b.view.y - pos.y];
                    return Math.hypot(dx1, dy1) - Math.hypot(dx2, dy2);
                });
                [collectedSymbol] = candidates;
            }
        }

        // Spawn explosion at collected symbol position (fallback to passed coords)
        if (featureFlags.useNewExplosion) {
            const textures = LuckyLennyAssetLoader.getTileExplosionTextures();

            if (textures && textures.length) {
                const explosion = new Explosion(textures);
                explosion.view.tint = getSymbolTint(symbol);

                const localPos = collectedSymbol
                    ? { x: collectedSymbol.view.x, y: collectedSymbol.view.y }
                    : pos;

                explosion.view.position.set(localPos.x, localPos.y);
                explosion.view.zIndex = LAYER_ORDER.MAIN_GAME;
                this.insideGameContainer.addChild(explosion.view);

                explosion.play();

                // Destroy after animation completes (AnimatedSprite emits complete)
                explosion.view.onComplete = () => {
                    explosion.destroy();
                };
            }
        } else {
            // Legacy Spine-based explosion
            const explosionSpine = LuckyLennyAssetLoader.getExplosionSpine();

            if (explosionSpine) {
                // Attempt to tint explosion using slot symbol color if possible.
                // Note: Spine tinting applies to slots; tint property may tint entire skeleton.
                (explosionSpine as unknown as { tint?: number }).tint = getSymbolTint(symbol);

                const localPos = collectedSymbol
                    ? { x: collectedSymbol.view.x, y: collectedSymbol.view.y }
                    : pos;

                explosionSpine.position.set(localPos.x, localPos.y);
                this.insideGameContainer.addChild(explosionSpine);

                // Play once and destroy on completion
                if (explosionSpine.state) {
                    explosionSpine.state.setAnimation(0, 'Fruit_Taken_Fx', false);
                    explosionSpine.state.timeScale = 1;

                    explosionSpine.state.addListener({
                        complete: () => {
                            this.insideGameContainer.removeChild(explosionSpine);
                            // Clear listeners to prevent leaks
                            explosionSpine.state.clearListeners();
                        },
                    });
                }
            }
        }

        const gameContainerYDuration = this.isTurboModeEnabled
            ? TURBO_GAME_CONTAINER_Y_POSITION_DURATION
            : GAME_CONTAINER_Y_POSITION_DURATION;

        gsap.to(this.insideGameContainer, {
            y: -pos.y * (this.isTurboModeEnabled ? TURBO_MODE_SPEED_FACTOR : 1),
            duration: gameContainerYDuration,
            ease: 'power1.out',
        });

        // Hide / collect the symbol (if found)
        if (collectedSymbol) {
            collectedSymbol.collect();
        }

        this.addSymbolToProgressbar(symbol);
    };

    public dispose() {
        this.isDisposing = true;

        // Unsubscribe from all event listeners
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        gsap.killTweensOf(this.insideGameContainer);
        gsap.killTweensOf(this.infiniteBackground);

        if (this.symbolManager) {
            this.symbolManager.dispose();
            this.symbolManager = null;
        }

        if (this.progressbarController) {
            // add dispose to progressbar
            this.viewport.layoutManager.unregister(this.progressbar.view);
            this.progressbarController.destroy();
            this.progressbarController = null;
        }

        if (this.multiplierController) {
            this.multiplierController.dispose();
            this.multiplierController = null;
        }

        if (this.infiniteBackground) {
            this.infiniteBackground.dispose();
            this.infiniteBackground = null;
        }

        // Cancel any pending show-symbols timeout to avoid callbacks after disposal
        if (this.showSymbolsTimeoutId !== undefined) {
            clearTimeout(this.showSymbolsTimeoutId);
            this.showSymbolsTimeoutId = undefined;
        }

        if (this.selectedSymbols) {
            this.selectedSymbols.length = 0;
            this.selectedSymbols = null;
        }

        if (this.targetSymbolPositions) {
            this.targetSymbolPositions.length = 0;
            this.targetSymbolPositions = null;
        }

        // Clear symbol configs
        if (this.symbolConfigs) {
            this.symbolConfigs.length = 0;
            this.symbolConfigs = null;
        }

        // First let base class handle generic cleanup (this also detaches Lenny)
        super.dispose();

        // Now it is safe to destroy the inside game container
        if (this.insideGameContainer) {
            Logger.log(
                'SlotGameManager: Disposing insideGameContainer (children already detached)',
            );
            this.insideGameContainer.destroy({ children: true });
            this.insideGameContainer = null;
        }
    }

    public onSpin(response: IPlayResponse): void {
        Logger.log(`SlotGameManager: onSpin: ${response}`);
        const symbols = ToSymbolType(response.lastRoundResult?.symbols);
        super.onSpin(response);

        this.progressbarController.reset();
        this.multiplierController.reset();

        // Build symbol grid from round result so development overrides (SymbolSetupPopup) work as expected
        this.generateSymbols(symbols);
        this.startGame();
    }
}
