import { Container } from 'pixi.js';
import gsap from 'gsap';
import { ICoordinates, Logger, Orientation } from '@bg-shared';
import { LuckyLennySound, luckyLennySoundsService } from '../../business/services/Sounds';
import { LENNY_CELEBRATION_HANDS_UP_TIMES, TURBO_MODE_SPEED_FACTOR } from '../../constants';
import { LAYER_ORDER } from '../config';
import { LuckyLennyEvents, SymbolType } from '../../enums';
import { Lenny } from '../objects/Lenny';
import type { ILuckyLennyEventManager, ILuckyLennyTurboEvent } from '../../interfaces';

type OnCollectCallback = (symbol: SymbolType, pos: ICoordinates) => void;

const BASIC_FLIGHT_SPEED = 775;
const TURBO_MODE_SPEED = BASIC_FLIGHT_SPEED / TURBO_MODE_SPEED_FACTOR;

export class LennyController {
    private lenny: Lenny;

    private xPosition = 0;

    private yPosition = 0;

    private isFlying = false;

    private onCollectCallbacks: OnCollectCallback[] = [];

    private flightSpeed = BASIC_FLIGHT_SPEED;

    private horizontalSpeedFactor = 2.5;

    private verticalSpeedFactor = 6.5;

    // Vertical movement animation properties
    private isVerticalMoving = false;

    private verticalMovementTween: gsap.core.Tween | null = null;

    private verticalMovementRange = 50; // How much Lenny moves up/down fake animation

    private verticalMovementDuration = 1.2; // Duration of one up/down cycle

    private currentSide: 'left' | 'right' | 'center' = 'center';

    private currentVerticalOffset = 0; // Current vertical offset for animation

    private parentLennyContainer?: Container;

    protected eventUnsubscribers: (() => void)[] = [];

    constructor(private readonly eventManager: ILuckyLennyEventManager) {
        this.lenny = new Lenny();
        this.lenny.setPosition(0, 0);
        this.lenny.startIdleAnimation();

        this.xPosition = 0;
        this.yPosition = 0;

        this.updatePosition();
        this.setupEvents();
    }

    private setupEvents(): void {
        this.eventUnsubscribers.push(
            this.eventManager.on<ILuckyLennyTurboEvent>(
                LuckyLennyEvents.turbo,
                (isTurbo: boolean) => {
                    this.flightSpeed = isTurbo ? TURBO_MODE_SPEED : BASIC_FLIGHT_SPEED;

                    this.lenny.setCelebrationCount(
                        LENNY_CELEBRATION_HANDS_UP_TIMES * (isTurbo ? TURBO_MODE_SPEED_FACTOR : 1),
                    );
                },
            ),
        );
    }

    public celebration() {
        this.lenny.stopIdleAnimation();
        this.lenny.celebrationAnimation();
    }

    // Move to a specific position
    public moveTo(x: number, y: number): void {
        // Always update internal position tracking
        this.xPosition = x;
        this.yPosition = y;

        // Update current side based on new position
        this.updateCurrentSide();

        // Update visual position immediately for normal movement
        // During collection flights, GSAP handles the visual position
        if (!this.isFlying) {
            this.updatePosition();
        }
    }

    public getPosition(): ICoordinates {
        return { x: this.xPosition, y: this.yPosition };
    }

    public getLenny(): Lenny {
        return this.lenny;
    }

    public isLennyFlying(): boolean {
        return this.isFlying;
    }

    public updatePosition(): void {
        this.lenny.setPosition(this.xPosition, this.yPosition);
    }

    public reset(): void {
        if (this.isFlying) {
            this.stopFlyingSounds();
        }

        this.isFlying = false;
        this.stopVerticalMovement();
        this.xPosition = 0;
        this.yPosition = 0;
        this.currentSide = 'center';
        this.currentVerticalOffset = 0;
        this.lenny.reset();
        this.updatePosition();
        this.lenny.startIdleAnimation();
    }

    public startFlyingToCollectWithPositions(
        symbolsToCollect: SymbolType[],
        symbolPositions: Array<ICoordinates>,
        onComplete: () => void,
        finalTargetPosition?: ICoordinates,
    ): void {
        if (this.isFlying) {
            this.stopFlyingSounds();
            this.isFlying = false;
            this.lenny.reset();
        }

        this.isFlying = true;
        this.lenny.startFlyingAnimation();

        luckyLennySoundsService.playLoop(LuckyLennySound.JetpackStart);

        // Start vertical movement animation when flying
        this.startVerticalMovement();

        if (!symbolPositions.length) {
            this.isFlying = false;
            this.stopFlyingSounds();
            this.stopVerticalMovement();
            if (onComplete) onComplete();
            return;
        }

        this.collectSymbolsSequentiallyWithPositions(
            symbolsToCollect,
            symbolPositions,
            0,
            onComplete,
            finalTargetPosition,
        );
    }

    private collectSymbolsSequentiallyWithPositions(
        symbolTypes: SymbolType[],
        symbolPositions: Array<ICoordinates>,
        currentIndex: number,
        onComplete: () => void,
        finalTargetPosition?: ICoordinates,
    ): void {
        if (currentIndex >= symbolPositions.length) {
            // All symbols collected, now fly to final target position if provided
            if (finalTargetPosition) {
                this.smoothCollectAtPosition(finalTargetPosition, null, () => {
                    this.isFlying = false;
                    this.stopFlyingSounds();
                    this.stopVerticalMovement();
                    this.lenny.startIdleAnimation();
                    onComplete?.();
                });
            } else {
                this.isFlying = false;
                this.stopFlyingSounds();
                this.stopVerticalMovement();
                this.lenny.startIdleAnimation();
                onComplete?.();
            }
            return;
        }

        const currentPosition = symbolPositions[currentIndex];
        const currentSymbolType = symbolTypes[currentIndex];

        this.smoothCollectAtPosition(currentPosition, currentSymbolType, () => {
            this.collectSymbolsSequentiallyWithPositions(
                symbolTypes,
                symbolPositions,
                currentIndex + 1,
                onComplete,
                finalTargetPosition,
            );
        });
    }

    private smoothCollectAtPosition(
        targetPosition: ICoordinates,
        symbolType: SymbolType | null,
        onComplete: () => void,
    ): void {
        const targetX = targetPosition.x;
        const targetY = targetPosition.y;
        const dx = Math.abs(targetX - this.xPosition);
        const dy = Math.abs(targetY - this.yPosition);
        const duration = Math.max(
            dx / (this.flightSpeed * this.horizontalSpeedFactor),
            dy / (this.flightSpeed * this.verticalSpeedFactor),
        );

        gsap.to(this, {
            duration,
            ease: 'none',
            xPosition: targetX,
            yPosition: targetY,
            onUpdate: () => {
                this.updatePosition();
                this.updateCurrentSide();
            },
            onComplete: () => {
                // Only trigger collection callbacks if this is a symbol collection (not final positioning)
                if (symbolType) {
                    this.onCollectCallbacks.forEach((cb) =>
                        cb(symbolType, { x: targetX, y: targetY }),
                    );

                    if (symbolType === SymbolType.RESPIN) {
                        this.eventManager.trigger(LuckyLennyEvents.respin);
                    }
                }

                setTimeout(onComplete, 10);
            },
        });
    }

    public addLennyToContainer(container: Container): void {
        container.addChild(this.lenny.container);
        this.lenny.container.zIndex = LAYER_ORDER.CHARACTER;
        this.lenny.startIdleAnimation();
    }

    public update(): void {
        // this.lenny.update();
    }

    /**
     * Apply blur effect to Lenny during fast movement
     */
    public applyBlurEffect(intensity = 2): void {
        this.lenny.applyBlurEffect(intensity);
    }

    /**
     * Remove blur effect from Lenny
     */
    public removeBlurEffect(): void {
        this.lenny.removeBlurEffect();
    }

    /**
     * Scale down Lenny to 50% of his normal size for bonus mini-games.
     */
    public scaleDownForBonusGame(scale = 0.8): void {
        this.lenny.scaleDownForBonusGame(scale);
    }

    public release(): void {
        Logger.log('LennyController: release - detaching Lenny from current parent');
        if (this.lenny.container.parent) {
            this.lenny.container.parent.removeChild(this.lenny.container);
            Logger.log('LennyController: release complete');
        } else {
            Logger.log('LennyController: release - no parent to detach from');
        }
    }

    public bringLennyToContainer(topLevelContainer: Container) {
        this.parentLennyContainer = this.lenny.container.parent;
        this.setLennyParentContainer(topLevelContainer);
    }

    public bringLennyBack() {
        this.setLennyParentContainer(this.parentLennyContainer);
    }

    private setLennyParentContainer(newContainer: Container) {
        const globalPoint = this.lenny.container.getGlobalPosition();

        newContainer.addChild(this.lenny.container);

        const localPoint = newContainer.toLocal(globalPoint);

        this.lenny.container.position.copyFrom(localPoint);
    }

    /**
     * Register callback executed each time Lenny collects a symbol.
     */
    public onCollect(callback: OnCollectCallback): void {
        this.onCollectCallbacks.push(callback);
    }

    /**
     * Start vertical movement animation based on current side
     */
    private startVerticalMovement(): void {
        if (this.isVerticalMoving || !this.isFlying) {
            return;
        }

        this.isVerticalMoving = true;
        this.currentVerticalOffset = 0;

        // Create a tween that updates the vertical offset value
        this.verticalMovementTween = gsap.to(this, {
            currentVerticalOffset: this.verticalMovementRange,
            duration: this.verticalMovementDuration / 2,
            ease: 'sine.inOut',
            yoyo: true,
            repeat: -1,
        });
    }

    /**
     * Stop vertical movement animation
     */
    private stopVerticalMovement(): void {
        if (this.verticalMovementTween) {
            this.verticalMovementTween.kill();
            this.verticalMovementTween = null;
        }
        this.currentVerticalOffset = 0;
        this.isVerticalMoving = false;
    }

    /**
     * Update current side based on x position
     */
    private updateCurrentSide(): void {
        const screenCenter = 0; // Assuming 0 is center
        const sideThreshold = 100; // Threshold to determine left/right side

        if (this.xPosition < screenCenter - sideThreshold) {
            this.currentSide = 'left';
        } else if (this.xPosition > screenCenter + sideThreshold) {
            this.currentSide = 'right';
        } else {
            this.currentSide = 'center';
        }
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        this.lenny.handleResize(orientation, isMobileView);
    }

    /**
     * Get current side
     */
    public getCurrentSide(): 'left' | 'right' | 'center' {
        return this.currentSide;
    }

    /**
     * Get the current vertical offset for the animation.
     */
    public getVerticalMovementOffset(): number {
        return this.currentVerticalOffset;
    }

    private stopFlyingSounds(): void {
        luckyLennySoundsService.stopLoop(LuckyLennySound.JetpackStart);
        luckyLennySoundsService.play(LuckyLennySound.JetpackStop);
    }

    public dispose(): void {
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        gsap.killTweensOf(this);
        this.lenny.dispose();
    }
}
