import { Container, Sprite } from 'pixi.js';
import gsap from 'gsap';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { Orientation } from '@bg-shared';
import { FADE_DURATION, TURBO_MODE_SPEED_FACTOR } from '../../constants';
import { LuckyLennySound, luckyLennySoundsService } from '../../business/services/Sounds';
import { TotalWin } from '../objects/overlay/TotalWin';
import { AbstractContainer } from '../objects/AbstractContainer';
import { LennyOverlay } from '../objects/overlay/LennyOverlay';
import { LuckyLennyAssetLoader } from '../utils/LuckyLennyAssetLoader';
import { WinAnimation } from '../objects/overlay/WinAnimation';
import type {
    ILuckyLennyEventManager,
    ILuckyLennyTotalWinEvent,
    ILuckyLennyWinEvent,
} from '../../interfaces';
import { Lucky<PERSON><PERSON>nyEvents, LuckyLennyGameId, WinTypes } from '../../enums';
import { LAYER_ORDER } from '../config';
import { TransitionController } from './TransitionController';

export class OverlayManager extends AbstractContainer {
    private readonly overlay: LennyOverlay;

    private gameId: LuckyLennyGameId = LuckyLennyGameId.LennySlotGame;

    private orientation: Orientation;

    private transitionController: TransitionController;

    private totalWinTimeoutId?: number;

    private isTurboModeEnabled = false;

    private tweenLogo?: gsap.core.Tween;

    private durationConfig = {
        totalWinDuration: 0,
        fadeDuration: 0,
    };

    protected eventUnsubscribers: (() => void)[] = [];

    constructor(
        private readonly stage: Container,
        private readonly viewport: PixiViewport,
        private readonly eventManager: ILuckyLennyEventManager,
    ) {
        super();

        this.overlay = new LennyOverlay(viewport, eventManager);

        // // Transition helper
        this.transitionController = new TransitionController(
            this.stage,
            this.viewport.width * 2,
            this.viewport.height * 2,
        );

        this.registerView();
        this.addEventListeners();
        this.updateDurationConfig();

        this.addChild(this.overlay.view);
        this.stage.addChild(this.view);

        this.zIndex = LAYER_ORDER.OVERLAY;
    }

    public turboModeEnable(enabled: boolean) {
        this.isTurboModeEnabled = enabled;
        this.updateDurationConfig();
    }

    public setGameId(gameId: LuckyLennyGameId) {
        this.gameId = gameId;
    }

    public isTransitioning(): boolean {
        return this.transitionController.isTransitioning();
    }

    /**
     *
     * When total win multiplier is in a range of:
     a) 0.00 - 24x ---> show regular animation (e.g. user places a bet of 1 eur and gets x20 multiplier - wins 20 eur. Multiplier is in a range of 0-24)
     b) 25x - 149x ---> show big win animation (e.g. user places a bet of 1 eur and gets x55 multiplier - wins 55 eur. Multiplier is in a range of 25-149)
     c) 150x - 299x ---> show mega win animation (e.g. user places a bet of 1 eur and gets x200 multiplier - wins 200 eur. Multiplier is in a range of 150-299)
     d) 300x - 977x ---> show ultra win animation (e.g. user places a bet of 1 eur and gets x600 multiplier across all games - wins 600 eur. Multiplier is in a range of 300-977)

     */

    /**
     * Perform a fade-out, show the game logo with zoom effect, execute the supplied action,
     * then fade-in again. Returns when the whole sequence is finished.
     */
    public performTransition(sprite: Sprite, action: () => void) {
        this.transitionController.fadeIn();
        this.eventManager.trigger(LuckyLennyEvents.switchGameAnimation, true);

        return this.showLogo(sprite).finally(() => {
            action();
            this.eventManager.trigger(LuckyLennyEvents.switchGameAnimation, false);

            return this.transitionController.fadeOut();
        });
    }

    private updateDurationConfig() {
        const totalWinDuration = 2000;
        const turboModeFactor = TURBO_MODE_SPEED_FACTOR;

        if (this.isTurboModeEnabled) {
            this.durationConfig.totalWinDuration = totalWinDuration * turboModeFactor;
            this.durationConfig.fadeDuration = FADE_DURATION * turboModeFactor;
        } else {
            this.durationConfig.totalWinDuration = totalWinDuration;
            this.durationConfig.fadeDuration = FADE_DURATION;
        }
    }

    private showLogo(sprite: Sprite): Promise<void> {
        this.transitionController.addAboveOverlay(sprite);

        return this.zoomAnimation(sprite).finally(() => {
            sprite.parent?.removeChild(sprite);
            sprite.destroy();
        });
    }

    private zoomAnimation(sprite: Sprite): Promise<void> {
        const LOGO_ASSET_GAP_Y = -40;

        if (sprite.label === String(LuckyLennyGameId.LennySlotGame)) {
            sprite.anchor.set(0.41, 0.5);
        }

        sprite.position.set(this.viewport.width * 0.5, this.viewport.height * 0.5);

        if (sprite.label !== String(LuckyLennyGameId.LennySlotGame)) {
            sprite.position.y += LOGO_ASSET_GAP_Y;
        }

        // note: must be called after setup sprite anchor
        // scale calculation depends on sprite anchor XY and sprite scale must be 1
        const scale = this.getLogoScale(sprite);

        sprite.scale.set(0);

        return new Promise((resolve) => {
            this.tweenLogo = gsap.to(sprite.scale, {
                x: scale.middle,
                y: scale.middle,
                duration: 0.3,
                ease: 'power2.out',
                onComplete: () => {
                    this.tweenLogo = gsap.to(sprite.scale, {
                        x: scale.finish,
                        y: scale.finish,
                        duration: 0.7,
                        ease: 'power2.in',
                        onComplete: resolve,
                    });
                },
            });
        });
    }

    public handleResize() {
        this.orientation =
            this.viewport.width >= this.viewport.height
                ? Orientation.LANDSCAPE
                : Orientation.PORTRAIT;
        this.overlay.handleResize(this.viewport.width, this.viewport.height);
        this.transitionController.onResize(this.viewport.width, this.viewport.height);
    }

    public destroy = () => {
        this.tweenLogo?.kill();
        clearTimeout(this.totalWinTimeoutId);

        this.viewport.layoutManager.unregister(this.view);
        this.overlay.destroy();
        super.destroy();

        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];
    };

    private addEventListeners = (): void => {
        this.eventUnsubscribers.push(
            this.eventManager.on<ILuckyLennyWinEvent>(
                LuckyLennyEvents.winAmount,
                ({ winAmount, multiplier, showPlusReSpin = false }) => {
                    const winType = [WinTypes.ULTRA, WinTypes.MEGA, WinTypes.BIG].find(
                        (type) => multiplier >= type,
                    );

                    const winAnimation = new WinAnimation({
                        spriteTextures: LuckyLennyAssetLoader.getWinTypeTextures(),
                        eventManager: this.eventManager,
                        orientation: this.orientation,
                        isTurboMode: this.isTurboModeEnabled,
                    });

                    winAnimation.setGameId(this.gameId);

                    this.addChild(winAnimation.view);

                    luckyLennySoundsService.handleWin(!!winType);

                    winAnimation.show({ winAmount, winType, showPlusReSpin }, () => {
                        luckyLennySoundsService.stop(LuckyLennySound.BigWinMusic);
                        winAnimation.destroy();
                    });
                },
            ),
            this.eventManager.on<ILuckyLennyTotalWinEvent>(
                LuckyLennyEvents.totalWin,
                ({ amount }) => {
                    clearTimeout(this.totalWinTimeoutId);

                    const { fadeDuration, totalWinDuration } = this.durationConfig;
                    const totalWin = new TotalWin({
                        amount,
                        fadeDuration,
                        orientation: this.orientation,
                    });

                    this.addChild(totalWin.view);

                    this.eventManager.trigger(LuckyLennyEvents.winAnimationStart);

                    totalWin.show();
                    this.eventManager.trigger(LuckyLennyEvents.totalWinAnimationStart);

                    luckyLennySoundsService.handleTotalWin();

                    this.totalWinTimeoutId = setTimeout(() => {
                        totalWin.hide(() => {
                            totalWin.destroy();
                            this.eventManager.trigger(LuckyLennyEvents.totalWinAnimationEnd);
                        });
                        this.eventManager.trigger(LuckyLennyEvents.winAnimationFadeOut);
                    }, totalWinDuration);
                },
            ),
        );
    };

    private registerView() {
        this.viewport.layoutManager.register(this.view, {
            horizontal: 'center',
            vertical: 'center',
        });
    }

    private getLogoScale(sprite: Sprite) {
        // note: assume that parent container and sprite have scale = 1
        const BEAUTY_RATIO = 1.12;
        const maxScaleW = Math.min(
            (this.viewport.width / (sprite.width * sprite.anchor.x * 2)) * BEAUTY_RATIO,
            1,
        );
        const maxScaleH = Math.min(this.viewport.height / (sprite.height * sprite.anchor.y * 2), 1);
        const scale = Math.min(maxScaleW, maxScaleH);

        return { middle: scale * 0.3, finish: scale };
    }
}
