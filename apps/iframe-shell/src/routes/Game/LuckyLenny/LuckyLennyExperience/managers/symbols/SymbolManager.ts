import { Container } from 'pixi.js';
import gsap from 'gsap';
import { ICoordinates, Orientation } from '@bg-shared';
import { isMobileView } from '@bg-shared/utils/isMobileView';
import { SymbolObject, ISymbolConfig } from '../../objects/Symbol';

import {
    SymbolLayoutManager,
    ArrangementVariant,
    ILayoutConfig,
    ISymbolPosition,
} from './SymbolLayoutManager';
import { SymbolType } from '../../../enums';

export { ArrangementVariant } from './SymbolLayoutManager';

export interface ISymbolColumn {
    x: number;
    symbols: SymbolObject[];
}

const desktopLayoutConfig: ILayoutConfig = {
    baseX: 650,
    startX: 650,
    startY: 200,
    columnWidth: 800,
    rowHeight: 250,
    columnCount: 10,
    rowCount: 9,
};

const mobileLayoutConfig: ILayoutConfig = {
    baseX: 400,
    startX: 350,
    startY: 200,
    columnWidth: 600,
    rowHeight: 400,
    columnCount: 10,
    rowCount: 9,
};

const getResponsiveLayoutConfig = (isMobile: boolean): ILayoutConfig =>
    isMobile ? mobileLayoutConfig : desktopLayoutConfig;

export class SymbolManager {
    public showSymbolDuration = 0.5;

    public oneSymbol = false; // Disable filtering - show all symbols but constrain target positions

    public range: [number, number] = [0, 2]; // Control which rows target symbols should appear in (bottom rows)

    private symbolsLocal: SymbolObject[] = [];

    private symbolsGrid: SymbolObject[][] = [];

    private symbolColumns: ISymbolColumn[] = [];

    private layoutManager: SymbolLayoutManager;

    private isMoving = false;

    private scrollSpeed = 3;

    private symbolConfigs: ISymbolConfig[] = [];

    private container: Container;

    private isMobile: boolean;

    private currentOrientation: Orientation = Orientation.LANDSCAPE;

    // Reverse lookup map for O(1) symbol-to-column lookup
    private symbolToColumnMap = new Map<SymbolObject, number>();

    constructor(container: Container, symbolConfigs: ISymbolConfig[]) {
        this.container = container;
        this.symbolConfigs = symbolConfigs;
        this.layoutManager = new SymbolLayoutManager();
        this.isMobile = isMobileView();
    }

    private getLayoutConfig(): ILayoutConfig {
        return getResponsiveLayoutConfig(this.isMobile);
    }

    public get symbols(): SymbolObject[] {
        return this.symbolsLocal;
    }

    public getSymbolColumns(): ISymbolColumn[] {
        return this.symbolColumns;
    }

    public setArrangementVariant(variant: ArrangementVariant): void {
        this.layoutManager.setArrangementVariant(variant);
    }

    public getCurrentVariant(): ArrangementVariant {
        return this.layoutManager.getCurrentVariant();
    }

    public clearSymbols(): void {
        this.symbolsLocal.forEach((s) => s.remove());
        this.symbolsLocal = [];
        this.symbolColumns = [];
        this.symbolsGrid = [];
        this.symbolToColumnMap.clear(); // Clear the reverse lookup map

        // Reset straight path when clearing symbols
        if (this.getCurrentVariant() === ArrangementVariant.STRAIGHT) {
            this.resetStraightPath();
        }
    }

    public startMoving(): void {
        this.isMoving = true;
    }

    public stopMoving(): void {
        this.isMoving = false;
    }

    public isSymbolsMoving(): boolean {
        return this.isMoving;
    }

    public setScrollSpeed(speed: number): void {
        this.scrollSpeed = speed;
    }

    public getScrollSpeed(): number {
        return this.scrollSpeed;
    }

    public createSymbolGrid(
        targetSymbols: SymbolType[] = [],
        lennyX = 0,
        maxXVariation?: number,
    ): void {
        this.clearSymbols();

        const layoutConfig = this.getLayoutConfig();
        layoutConfig.startX = lennyX + layoutConfig.baseX;

        // Add maxXVariation to config if provided and using STRAIGHT variant
        if (
            maxXVariation !== undefined &&
            this.getCurrentVariant() === ArrangementVariant.STRAIGHT
        ) {
            layoutConfig.maxXVariation = maxXVariation;
        }

        const positions = this.layoutManager.generateSymbolPositions(
            layoutConfig,
            targetSymbols,
            this.range,
        );

        this.createSymbolsFromPositions(positions);
    }

    public createSymbolRowsWithTargets(targetSymbols: SymbolType[]): void {
        if (!targetSymbols.length) {
            return;
        }

        // Use PREDEFINED variant for target symbol placement
        const currentVariant = this.getCurrentVariant();
        this.setArrangementVariant(ArrangementVariant.PREDEFINED);

        // Configure layout for collection scenario using responsive config
        const baseConfig = this.getLayoutConfig();
        const collectionConfig: ILayoutConfig = {
            ...baseConfig,
            startX: 0, // Start from (0,0) for collection
            columnCount: targetSymbols.length,
            // Use responsive column width and row height based on device and orientation
            columnWidth: baseConfig.columnWidth,
            rowHeight: baseConfig.rowHeight,
        };

        const positions = this.layoutManager.generateSymbolPositions(
            collectionConfig,
            targetSymbols,
            this.range,
        );

        this.createSymbolsFromPositions(positions);

        // Restore original variant
        this.setArrangementVariant(currentVariant);
    }

    /**
     * Reset the straight path (useful when starting a new game)
     */
    public resetStraightPath(): void {
        this.layoutManager.resetStraightPath();
    }

    /**
     * Get the current straight path for debugging/visualization
     */
    public getStraightPath(): number[] {
        return this.layoutManager.getStraightPath();
    }

    /**
     * Validate STRAIGHT variant path generation
     */
    public validateStraightPath(targetSymbols: SymbolType[]): boolean {
        const layoutConfig = this.getLayoutConfig();
        return this.layoutManager.validateStraightPath(targetSymbols, layoutConfig.rowCount);
    }

    public getTargetSymbolPositions = (selectedSymbols: SymbolType[]): Array<ICoordinates> => {
        const positions: Array<ICoordinates> = [];

        // For STRAIGHT variant, use the generated path to get positions in correct order
        if (this.getCurrentVariant() === ArrangementVariant.STRAIGHT) {
            const straightPath = this.getStraightPath();

            // Get positions based on the straight path
            selectedSymbols.forEach((targetSymbol, index) => {
                if (index < straightPath.length) {
                    const targetRow = straightPath[index];
                    const symbol = this.getSymbolAt(index, targetRow);

                    if (symbol && symbol.type === targetSymbol) {
                        positions.push({ x: symbol.view.x, y: symbol.view.y });
                    }
                }
            });
        } else {
            // For other variants, use the original column-wise traversal
            this.symbolsGrid.forEach((column, columnIndex) => {
                column.forEach((symbol) => {
                    if (symbol && symbol.type === selectedSymbols[columnIndex]) {
                        positions.push({ x: symbol.view.x, y: symbol.view.y });
                    }
                });
            });
        }

        return positions;
    };

    /**
     * Create symbols from position data
     */
    private createSymbolsFromPositions(positions: ISymbolPosition[]): void {
        // Track which symbols have been marked as targets to prevent duplicates
        const targetSymbolsPerColumn = new Map<number, Set<SymbolType>>();

        // Group positions by column for column management
        const columnGroups = new Map<number, ISymbolPosition[]>();

        positions.forEach((position) => {
            if (!columnGroups.has(position.columnIndex)) {
                columnGroups.set(position.columnIndex, []);
            }
            columnGroups.get(position.columnIndex)?.push(position);
        });

        // Create symbols and organize into columns
        columnGroups.forEach((columnPositions, columnIndex) => {
            const symbols: SymbolObject[] = [];
            const columnX = columnPositions[0]?.x || 0;

            // Ensure grid array for this column exists
            if (!this.symbolsGrid[columnPositions[0].columnIndex]) {
                this.symbolsGrid[columnPositions[0].columnIndex] = [];
            }

            // Initialize target tracking for this column
            if (!targetSymbolsPerColumn.has(columnIndex)) {
                targetSymbolsPerColumn.set(columnIndex, new Set());
            }

            columnPositions
                .sort((a, b) => a.rowIndex - b.rowIndex) // Ensure proper row order
                .forEach((position) => {
                    const config = this.symbolConfigs.find((c) => c.type === position.symbolType);

                    if (!config) {
                        return;
                    }

                    const symbol = new SymbolObject(
                        this.container,
                        position.symbolType,
                        config,
                        position.x,
                        position.y,
                    );

                    // Apply initial responsive scaling based on current device settings
                    symbol.handleResize(this.currentOrientation, this.isMobile);

                    // Start symbols completely hidden for show animation
                    symbol.view.visible = false;
                    symbol.view.scale.set(0, 0);

                    // Check if this should be marked as a target symbol
                    if (position.isTarget) {
                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                        const columnTargets = targetSymbolsPerColumn.get(columnIndex)!;

                        // For STRAIGHT variant, we need to be more careful about which symbols to mark
                        // Only mark as target if this is the FIRST occurrence of this symbol type in this column
                        // AND it's at the correct row position according to the straight path
                        if (!columnTargets.has(position.symbolType)) {
                            symbol.isTargetSymbol = true;
                            columnTargets.add(position.symbolType);
                        } else {
                            // This symbol type already exists in this column, but we need to check if this one should be the target
                            // For STRAIGHT variant, we should mark the one at the correct row position
                            const layoutManager = this.getLayoutManager();
                            if (layoutManager.getCurrentVariant() === ArrangementVariant.STRAIGHT) {
                                const straightPath = layoutManager.getStraightPath();
                                if (
                                    columnIndex < straightPath.length &&
                                    position.rowIndex === straightPath[columnIndex]
                                ) {
                                    // This is the correct position for the target, mark it
                                    symbol.isTargetSymbol = true;
                                }
                            }
                        }
                    }

                    if (symbol.spine) {
                        symbol.spine.state.setAnimation(0, 'Idle', true);
                    }

                    symbols.push(symbol);
                    this.symbolsLocal.push(symbol);

                    // Place into grid for direct access
                    if (!this.symbolsGrid[position.columnIndex]) {
                        this.symbolsGrid[position.columnIndex] = [];
                    }
                    this.symbolsGrid[position.columnIndex][position.rowIndex] = symbol;

                    // Add to the reverse lookup map for O(1) column lookup
                    this.symbolToColumnMap.set(symbol, position.columnIndex);
                });

            this.symbolColumns.push({ x: columnX, symbols });
        });
    }

    public update(deltaTime: number): void {
        if (!this.isMoving) {
            return;
        }

        this.symbolColumns.forEach((column) => {
            column.x -= this.scrollSpeed * deltaTime * 60;
            column.symbols.forEach((symbol) => {
                symbol.updatePosition(column.x, symbol.view.y);
            });
        });

        this.recycleOffscreenColumns();
    }

    private recycleOffscreenColumns(): void {
        const layoutConfig = this.getLayoutConfig();
        const { columnWidth } = layoutConfig;

        const rightmostX = this.symbolColumns.reduce(
            (maxX, { x }) => (x > maxX ? x : maxX),
            -Infinity,
        );

        this.symbolColumns.forEach((column) => {
            if (column.x < -columnWidth) {
                column.x = rightmostX + columnWidth;
                column.symbols.forEach((symbol) => {
                    symbol.updatePosition(column.x, symbol.view.y);
                });
            }
        });
    }

    public hideSymbols(): void {
        // Animate symbols out with scale down effect, but only for symbols that haven't been collected
        this.symbolsLocal.forEach((symbol, index) => {
            // Skip symbols that are already collected (they should already be hidden)
            if (symbol.isCollected) {
                return;
            }

            symbol.updateSpineScale();
            symbol.updateSpriteSize();
            gsap.to(symbol.view.scale, {
                x: 0,
                y: 0,
                duration: 0.3,
                delay: index * 0.02, // Stagger the animations
                ease: 'back.in(1.7)',
                onComplete: () => {
                    symbol.view.visible = false;
                },
            });
        });
    }

    public showSymbols(duration = this.showSymbolDuration): void {
        // Single loop: determine visibility AND start animations immediately
        this.symbolsLocal.forEach((symbol, index) => {
            symbol.view.visible = true;
            symbol.view.scale.set(0, 0);

            let shouldShow = true;

            // Check if this symbol should be shown based on oneSymbol flag
            if (this.oneSymbol) {
                const symbolColumn = this.getSymbolColumnForSymbol(symbol);
                const isFirstColumn = symbolColumn === 0;
                shouldShow = isFirstColumn || symbol.isTargetSymbol;

                if (!shouldShow) {
                    symbol.view.visible = false;
                    symbol.view.scale.set(0, 0);
                }
            }

            if (shouldShow) {
                gsap.to(symbol.view.scale, {
                    x: 1,
                    y: 1,
                    duration,
                    delay: duration === 0 ? 0 : index * 0.01,
                    ease: 'back.out(1.7)',
                    onComplete: () => {
                        // Apply responsive scaling after animation completes
                        symbol.updateSpineScale();
                        symbol.updateSpriteSize();
                    },
                });
            }
        });
    }

    /**
     * Helper method to find which column a symbol belongs to
     * Uses O(1) lookup via reverse mapping instead of O(n) iteration
     */
    private getSymbolColumnForSymbol(symbol: SymbolObject): number {
        return this.symbolToColumnMap.get(symbol) ?? 0; // Default to first column if not found
    }

    public findSymbolByCoordinates(x: number, y: number): SymbolObject | null {
        return (
            this.symbolsLocal.find((symbol) => {
                const bounds = symbol.getBounds();
                return (
                    x >= bounds.left && x <= bounds.right && y >= bounds.top && y <= bounds.bottom
                );
            }) || null
        );
    }

    public getTargetSymbolsInOrder(): SymbolObject[] {
        return this.symbolsLocal
            .filter((symbol) => symbol.isTargetSymbol && !symbol.isCollected)
            .sort((a, b) => a.view.x - b.view.x);
    }

    /**
     * Get layout manager for advanced configuration
     */
    public getLayoutManager(): SymbolLayoutManager {
        return this.layoutManager;
    }

    public dispose(): void {
        this.symbolsLocal.forEach((symbol) => {
            gsap.killTweensOf(symbol.view.scale);
            symbol.dispose();
        });
        this.symbolsLocal = [];
        this.symbolsGrid = [];
        this.symbolColumns = [];
        this.symbolToColumnMap.clear(); // Clear the reverse lookup map
    }

    public reset(): void {
        this.symbolsLocal = [];
        this.symbolsGrid = [];
        this.symbolColumns = [];
        this.symbolToColumnMap.clear(); // Clear the reverse lookup map
    }

    /**
     * Public accessor for grid (read-only)
     */
    public getSymbolsGrid(): ReadonlyArray<ReadonlyArray<SymbolObject | undefined>> {
        return this.symbolsGrid;
    }

    /**
     * Convenience helper – fetch symbol at column/row, undefined if not present
     */
    public getSymbolAt(col: number, row: number): SymbolObject | undefined {
        return this.symbolsGrid[col]?.[row];
    }

    public handleResize(orientation?: Orientation, isMobileViewParam?: boolean): void {
        const prevIsMobile = this.isMobile;
        const prevOrientation = this.currentOrientation;

        this.isMobile = isMobileViewParam;
        if (orientation !== undefined) {
            this.currentOrientation = orientation;
        }

        const deviceChanged =
            prevIsMobile !== this.isMobile || prevOrientation !== this.currentOrientation;

        // If the device type / orientation changed, rebuild the whole layout once instead of
        // updating every symbol first (the symbols will be recreated anyway).
        if (deviceChanged && this.symbolsLocal.length > 0) {
            this.updateLayoutForCurrentDevice();
            return; // early-exit – symbols were recreated, nothing else to update now
        }

        // Otherwise just forward the resize event to existing symbols.
        this.symbolsLocal.forEach((symbol) => {
            symbol.handleResize(orientation, isMobileViewParam);
        });
    }

    /**
     * Update layout configuration when switching between desktop and mobile
     */
    private updateLayoutForCurrentDevice(): void {
        const layoutConfig = this.getLayoutConfig();

        // Update existing symbols with new layout configuration
        if (this.symbolsGrid.length > 0) {
            // Recalculate positions based on new layout
            const currentSymbols = this.symbolsLocal.map((symbol) => symbol.type);
            this.createSymbolGrid(currentSymbols, layoutConfig.startX - layoutConfig.baseX);

            // Newly created symbols are invisible (scale 0) by default; reveal them
            this.showSymbols();
        }
    }
}
