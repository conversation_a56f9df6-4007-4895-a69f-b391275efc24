import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { Logger, Orientation } from '@bg-shared';
import { EventManager } from '@bg-services';
import { BlurFilter, Container } from 'pixi.js';
import { LuckyLennyGameId, LuckyLennyGameState, NextAction, PixiEventMode } from '../../../enums';
import { ArrangementVariant } from '../symbols/SymbolLayoutManager';
import { LennyController } from '../LennyController';
import type { IPlayResponse } from '../../../interfaces';
import { BonusGame } from '../../objects/BonusGame';

export class GameManagerBase {
    protected gameId: LuckyLennyGameId;

    protected activeGameContainer: Container | null = new Container();

    protected currentState: LuckyLennyGameState = LuckyLennyGameState.IDLE;

    protected lennyController: Lenny<PERSON>ontroller;

    protected isPaused = false;

    protected debug = false;

    protected totalWinAmount: number;

    protected isReady = false;

    protected isMobileView = false;

    protected orientation: Orientation = Orientation.LANDSCAPE;

    protected nextAction: NextAction;

    protected winAmount: number;

    protected result: number[];

    protected lastSlotGameResponse: number[];

    protected response: IPlayResponse;

    protected bonusGame: BonusGame | null = null;

    protected isTurboModeEnabled = false;

    private readonly blurOptions = {
        strength: 8,
        quality: 4,
    };

    public get id(): LuckyLennyGameId {
        return this.gameId;
    }

    constructor(
        protected readonly stage: Container,
        protected readonly viewport: PixiViewport,
        protected readonly eventManager: EventManager,
    ) {
        this.stage.addChild(this.activeGameContainer);
        this.activeGameContainer.eventMode = PixiEventMode.passive;
        this.activeGameContainer.interactive = true;
    }

    public blur(enable: boolean) {
        const filters = enable ? this.blurFilter : undefined;

        this.activeGameContainer.filters = filters;

        if (this.bonusGame) {
            this.bonusGame.filters = filters;
        }
    }

    // todo: workaround to hide main game
    public set visible(value: boolean) {
        this.activeGameContainer.visible = value;
    }

    public turboModeEnable(enable: boolean) {
        this.isTurboModeEnabled = enable;
    }

    public setup(symbols: number[]): void {
        this.lastSlotGameResponse = symbols;
    }

    public update(): void {
        this.lennyController?.update();
    }

    public onSpin(response: IPlayResponse): void {
        this.response = response;
        this.nextAction = response.nextAction;
        this.winAmount = response.amountToCollect;
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public onBonusGameboardClickResponse(response: IPlayResponse, symbolIndex = 0): void {
        this.nextAction = response.nextAction;
    }

    public dispose(): void {
        this.lennyController.release();
        this.viewport.layoutManager.unregister(this.activeGameContainer);
        this.activeGameContainer.parent?.removeChild(this.activeGameContainer);
        this.activeGameContainer.removeChildren();
        this.activeGameContainer.destroy({ texture: true, children: true, context: true });
        this.activeGameContainer = null;
        this.bonusGame?.destroy();
    }

    public reset(): void {}

    public activeLenny(lennyController: LennyController): void {
        this.lennyController = lennyController;
    }

    public startGame(): void {}

    public setArrangementVariant(newVariant?: ArrangementVariant): void {
        Logger.log(`GameManagerBase: setArrangementVariant: ${newVariant}`);
    }

    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        if (orientation !== undefined) {
            this.orientation = orientation;
        }
        if (isMobileView !== undefined) {
            this.isMobileView = isMobileView;
        }

        this.bonusGame?.handleResize(orientation, isMobileView);
    }

    protected get blurFilter() {
        return [new BlurFilter(this.blurOptions)];
    }
}
