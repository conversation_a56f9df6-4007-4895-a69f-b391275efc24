import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { Container } from 'pixi.js';
import { random } from 'lodash-unified';
import { Logger, Orientation } from '@bg-shared';
import { LuckyLennySound, luckyLennySoundsService } from '../../../../business/services/Sounds';
import {
    ILuckyLennyEventManager,
    ILuckyLennySwitchGameEvent,
    ILuckyLennyWinEvent,
    IPlayResponse,
} from '../../../../interfaces';
import { gameConfigSelectors } from '../../../../business/stores/gameConfig.selectors';
import { LuckyLennyEvents, LuckyLennyGameId, NextAction } from '../../../../enums';
import { BonusGame } from '../../../objects/BonusGame';
import { Wheel } from '../../../objects/bonusWheel/Wheel';
import type { IGameManager } from '../../../interfaces/gameManager';
import { LuckyLennyAssetLoader } from '../../../utils/LuckyLennyAssetLoader';
import { GameManagerBase } from '../GameManagerBase';
import { WheelSymbolsConfig } from './config';
import { LennyController } from '../../LennyController';
import { ParticleSystem } from '../../../objects/particle';
import particlesConfig from '../../../config/particlesConfig';

type Segment = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface IWheelSegment {
    multiplier: number;
    bonusRound?: 'wheel' | 'gameboard';
    segmentIndex: number;
    originalIndex: number; // Preserve the original index from backend
}

export class BonusWheelController extends GameManagerBase implements IGameManager {
    private readonly game: Wheel;

    private targetSegment: Segment;

    private targetMultiplier: number;

    private hasSpin = false;

    private shouldRandomize = false; // Flag to control randomization

    private wheelSegments: IWheelSegment[] = [];

    private particleSystem: ParticleSystem;

    // Store unsubscribe functions for event listeners
    private eventUnsubscribers: ReturnType<ILuckyLennyEventManager['on']>[] = [];

    constructor(
        stage: Container,
        viewport: PixiViewport,
        eventManager: ILuckyLennyEventManager,
        debug = false,
        shouldRandomize = false,
    ) {
        super(stage, viewport, eventManager);

        this.debug = debug;
        this.shouldRandomize = shouldRandomize;

        this.game = new Wheel(LuckyLennyAssetLoader.getBonusWheelTextures());

        this.bonusGame = new BonusGame(this.game, this.stage, this.viewport);

        this.activeGameContainer.addChild(this.bonusGame.view);

        // Initialize particle system for firefly effect
        this.particleSystem = new ParticleSystem({
            ...particlesConfig,
            enabled: true,
            ticker: this.viewport.ticker,
        });
        this.activeGameContainer.addChild(this.particleSystem.container);

        // Set initial bounds for particle system
        this.particleSystem.setBounds({
            x: -this.viewport.width / 2,
            y: -this.viewport.height / 2,
            width: this.viewport.width,
            height: this.viewport.height,
        });

        // Initialize the particle system after it's added to the scene
        this.particleSystem.initialize();

        this.viewport.layoutManager.register(this.activeGameContainer);
        this.viewport.layoutManager.update();

        this.game.addStopSpinListener(() => this.handleStopSpin());

        this.eventUnsubscribers.push(
            this.eventManager.on(LuckyLennyEvents.winAnimationEnd, this.winAnimationEnd.bind(this)),
        );

        // Initialize wheel with randomized segments from backend config
        this.setup();
    }

    public registerTopLevelContainer(topLevelContainer: Container) {
        this.viewport.layoutManager.register(topLevelContainer);
    }

    /**
     * Sets up the wheel with randomized segments from backend data
     * @param wheelSymbols - Array of wheel symbols from backend
     */
    public setupWheelSegments(wheelSymbols: typeof WheelSymbolsConfig) {
        this.wheelSegments = wheelSymbols.map((symbol) => ({
            multiplier: symbol.multiplier || 1,
            bonusRound: symbol.bonusRound,
            segmentIndex: symbol.fid, // Use fid as segment index
            originalIndex: symbol.fid, // Use fid as original index
        }));
    }

    /**
     * Sets up the wheel for testing purposes (creates random respins)
     * @param respins - Number of respins to generate
     */
    private setupTestWheel(respins = 3) {
        // Create test wheel with random respins but fixed positions
        const testSegments = [];
        for (let i = 0; i < respins; i++) {
            const randomMultiplier = [1, 5, 7, 10, 15, 25, 27, 30, 50, 100][
                Math.floor(Math.random() * 10)
            ];
            testSegments.push({
                multiplier: randomMultiplier,
                segmentIndex: i,
                originalIndex: i,
            });
        }
        this.wheelSegments = testSegments;
    }

    /**
     * Sets the debug mode for the wheel controller
     * @param debug - Whether to enable debug mode
     */
    public setDebugMode(debug: boolean) {
        this.debug = debug;
    }

    public setup() {
        // If debug is true, create test wheel
        if (this.debug) {
            const respins = random(1, 3);
            this.setupTestWheel(respins);
            return;
        }

        this.setupWheelSegments(WheelSymbolsConfig);
    }

    public dispose() {
        // Unsubscribe from all event listeners
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        // Clean up particle system
        if (this.particleSystem) {
            this.particleSystem.destroy();
            this.particleSystem = null;
        }

        super.dispose();
    }

    /**
     * Enable particle effects
     */
    public enableParticles(): void {
        if (this.particleSystem) {
            this.particleSystem.setEnabled(true);
        }
    }

    /**
     * Disable particle effects
     */
    public disableParticles(): void {
        if (this.particleSystem) {
            this.particleSystem.setEnabled(false);
        }
    }

    /**
     * Set particle intensity (spawn rate)
     */
    public setParticleIntensity(intensity: number): void {
        if (this.particleSystem) {
            this.particleSystem.setSpawnRate(intensity);
        }
    }

    public onSpin(response: IPlayResponse) {
        this.winAmount = gameConfigSelectors.getLastGameStateWinAmount(response);
        this.totalWinAmount = gameConfigSelectors.getTotalAmountWon(response);
        // If we have a result from backend, use it to determine the target segment
        if (response.lastRoundResult?.symbols && response.lastRoundResult?.symbols.length > 0) {
            const backendIndex = response.lastRoundResult?.symbols[0]; // Backend sends the index of the wheel symbol
            const targetSegment = this.findSegmentByIndex(backendIndex);

            this.hasSpin = response.nextAction === NextAction.WHEEL;
            if (targetSegment !== null) {
                this.targetMultiplier = targetSegment.multiplier;
                this.targetSegment = targetSegment.segmentIndex as Segment;

                this.game.spin(this.targetSegment);
                luckyLennySoundsService.play(LuckyLennySound.WheelSpin);
                return;
            }

            console.error(
                `Backend index ${backendIndex} not found in wheel segments. Available original indices: ${this.wheelSegments
                    .map((seg) => seg.originalIndex)
                    .join(', ')}`,
            );
        }
        // Fallback to local wheel segments if no valid backend result
        this.spin();
    }

    private findSegmentByIndex(index: number): IWheelSegment | null {
        // Get the wheel symbol from config using backend index
        const wheelSymbol = WheelSymbolsConfig[index];

        if (!wheelSymbol) {
            console.error(`Backend index ${index} not found in WheelSymbolsConfig`);
            return null;
        }

        // Use the fid (frontend ID) from the config to find the segment
        const segment = this.wheelSegments.find((seg) => seg.originalIndex === wheelSymbol.fid);

        return segment || null;
    }

    private spin() {
        const segment = this.wheelSegments.shift();

        if (!segment) {
            console.error('No wheel segments available for spin');
            return;
        }

        this.setupSpin(segment);
        this.game.spin(this.targetSegment);
        luckyLennySoundsService.play(LuckyLennySound.WheelSpin);
    }

    private setupSpin(segment: IWheelSegment) {
        this.targetMultiplier = segment.multiplier;
        this.targetSegment = segment.segmentIndex as Segment;
    }

    private handleStopSpin() {
        luckyLennySoundsService.stop(LuckyLennySound.WheelSpin);
        if (this.targetMultiplier === 1) {
            this.eventManager.trigger<ILuckyLennySwitchGameEvent>(LuckyLennyEvents.switchGame, {
                gameId: LuckyLennyGameId.LennyAztecGame,
            });
            luckyLennySoundsService.play(LuckyLennySound.GameboardWin);
            return;
        }

        this.showWin();
    }

    private showWin() {
        this.eventManager.trigger<ILuckyLennyWinEvent>(LuckyLennyEvents.winAmount, {
            winAmount: this.winAmount,
            multiplier: this.targetMultiplier,
            showPlusReSpin: this.hasSpin,
        });

        luckyLennySoundsService.play(
            this.hasSpin ? LuckyLennySound.MultiplierPlusRespinWin : LuckyLennySound.MultiplierWin,
        );
    }

    private winAnimationEnd() {
        if (this.hasSpin) {
            return;
        }

        this.eventManager.trigger<ILuckyLennySwitchGameEvent>(LuckyLennyEvents.switchGame, {
            gameId: LuckyLennyGameId.LennySlotGame,
            totalWinAmount: this.totalWinAmount,
        });
    }

    public activeLenny(lennyController: LennyController): void {
        super.activeLenny(lennyController);

        if (!this.activeGameContainer) {
            Logger.error(
                'BonusBoardGameController: activeLenny - game.board.view is null, cannot add lenny to game',
            );
            return;
        }
        this.lennyController.addLennyToContainer(this.activeGameContainer);
        if (this.isMobileView) {
            this.lennyController.moveTo(-350, 500);
            this.lennyController.scaleDownForBonusGame(0.6);
        } else {
            this.lennyController.moveTo(-400, 200);
            this.lennyController.scaleDownForBonusGame(1);
        }
    }

    /**
     * Gets the current wheel segments for debugging
     */
    public getWheelSegments(): IWheelSegment[] {
        return [...this.wheelSegments];
    }

    /**
     * Gets the current wheel configuration for debugging
     */
    public getWheelConfiguration() {
        return {
            segments: this.wheelSegments,

            shouldRandomize: this.shouldRandomize,
            originalToSegmentMapping: this.wheelSegments.reduce(
                (acc, seg) => {
                    acc[seg.originalIndex] = seg.segmentIndex;
                    return acc;
                },
                {} as Record<number, number>,
            ),
            wheelSymbolsConfig: WheelSymbolsConfig,
        };
    }

    /**
     * Handle resize events and update particle system bounds
     */
    public handleResize(orientation?: Orientation, isMobileView?: boolean): void {
        super.handleResize(orientation, isMobileView);

        // Update particle system bounds to match viewport
        if (this.particleSystem && !this.particleSystem.container.destroyed) {
            this.particleSystem.updateBounds(this.viewport);
        }
    }
}
