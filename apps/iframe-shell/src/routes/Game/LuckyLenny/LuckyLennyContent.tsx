import { ElementRef, JS<PERSON>, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { useStore } from '@betgames/bg-state-manager';
import { LiveInfo, TopNotifications } from '@bg-components/GameContent';
import {
    AutoPick,
    AutoPlay,
    Burger,
    Controls,
    InfoMessage,
    PlayerInfo,
    RGSLoadingScreen,
    SettingsModal,
    Sound,
    Spin,
    StakeSelection,
    Turbo,
    useAutoPlay,
} from '@bg-components/RGS';
import { useHandleError } from '@bg-components/ErrorBoundary';
import { RotateRestriction } from '@bg-components/RotateRestriction';
import { stopAutoPlayOnInsufficientBalance } from '@bg-components/RGS/AutoPlayModal/utils/stopAutoPlayOnInsufficientBalance';
import {
    balanceStore,
    betAmountEntity,
    CONTAINER_WIDTH,
    envConfig,
    formatToUserCurrency,
    GameId,
    gamesStatusEntity,
    gamesStatusSelectors,
    Logger,
    partnerSettingsStore,
    rgsAutoPlayEntity,
    t,
    useInitialBetAmount,
    useTopNotification,
    rgsFreePlayEntity,
    rgsBalanceEntity,
    useModalsManager,
    ModalName,
} from '@bg-shared';
import { EventManager } from '@bg-services';
import { useLandscape, useMobileView } from '@betgames/bg-tools';
import { useFreePlay } from '@iframe-shell/routes/Game/LuckyLenny/hooks/useFreePlay';
import { LuckyLennyAssetLoader } from './LuckyLennyExperience/utils/LuckyLennyAssetLoader';
import { mapBoardSymbolIndexToMultiplier } from './LuckyLennyExperience/utils/bonusBoardMappings';
import { gameApi } from './business/services/GameApi/Game.api';
import { gameConfig } from './business/stores/GameConfig.store';
import { gameConfigSelectors } from './business/stores/gameConfig.selectors';
import LennyExperience from './LennyExperience';
import {
    ILuckyLennyBonusGameboardClickEvent,
    ILuckyLennyEventManager,
    ILuckyLennySwitchGameAnimationEvent,
    ILuckyLennySwitchGameEvent,
    ILuckyLennyTurboEvent,
    IPlayResponse,
} from './interfaces';
import { LuckyLennyEvents, LuckyLennyGameId, NextAction } from './enums';
import {
    LuckyLennySound,
    luckyLennySoundsService,
    useBackgroundMusic,
} from './business/services/Sounds';
import { ErrorModal, FreePlayModal, RespinModal, UnfinishedGameModal } from './components/Modal';
import classes from './LuckyLennyContent.module.scss';

const BALANCE_UPDATE_DELAY = 1000;

const RESPIN_MODAL_SHOW_TIME = 2000;

const gameId = GameId.LUCKY_LENNY;

export const LuckyLennyContent = (): JSX.Element => {
    const topNotification = useTopNotification();
    const config = useStore(gameConfig);
    const openForBets = useStore(
        gamesStatusEntity.store(gameId),
        gamesStatusSelectors.getOpenForBets,
    );
    const balance = useStore(rgsBalanceEntity.store(gameId));
    const betAmount = useStore(betAmountEntity.store(gameId));
    const isMobileView = useMobileView(CONTAINER_WIDTH);
    const isLandscape = useLandscape();
    const eventManagerRef = useRef<ILuckyLennyEventManager>(null);
    const experienceRef = useRef<LennyExperience>(null);
    const stakes = gameConfigSelectors.getStakes(config);

    useInitialBetAmount(gameId, stakes, true);
    const [isTurbo, setIsTurbo] = useState<boolean>(false);
    const [mounted, setMounted] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);
    const [isSwitchGameAnimating, setSwitchGameAnimating] = useState<boolean>(false);
    const [luckyLennyGame, setLuckyLennyGame] = useState<LuckyLennyGameId>(
        LuckyLennyGameId.LennySlotGame,
    );
    const luckyLennyGameRef = useRef<LuckyLennyGameId>(luckyLennyGame);
    luckyLennyGameRef.current = luckyLennyGame;
    const [showSplashscreen, setShowSplashscreen] = useState<boolean>(true);
    const [showUnfinishedGameModal, setShowUnfinishedGameModal] = useState<boolean>(false);
    const { error, handleError } = useHandleError();
    const [showRespinModal, setShowRespinModal] = useState<boolean>(false);
    const canvasRef = useRef<ElementRef<'canvas'>>(null);
    const { isDevEnv } = envConfig;
    const isDebugMode =
        (isDevEnv && new URLSearchParams(window.location.search).get('debug') === 'true') || false;
    const isMaintenance = false;

    const setOpenForBets = useCallback((value: boolean) => {
        gamesStatusEntity.setStarted(gameId, !value);
        gamesStatusEntity.setOpenForBets(gameId, value);
    }, []);

    const [totalWinAmount, setTotalWinAmount] = useState<number>(0);
    const totalWinAmountRef = useRef<number>(0);
    const [pendingRespin, setPendingRespin] = useState<boolean>(false);
    const pendingRespinRef = useRef<boolean>(false);
    pendingRespinRef.current = pendingRespin;
    const [shouldValidateBalance, setShouldValidateBalance] = useState<boolean>(true);
    const modalsManager = useModalsManager();

    const {
        showModal: showFreePlayModal,
        pendingShowModal: pendingShowFreePlayModal,
        freePlay,
        handleModalDismiss: handleFreePlayModalDismiss,
    } = useFreePlay({
        gameId,
        eventManager: eventManagerRef.current,
        openForBets,
        currentGame: luckyLennyGame,
        pendingRespin,
    });

    const howToPlayReplacements = useMemo(
        () => ({
            rtp: gameConfig.value.config.config.expectedRtp,
            minStake: formatToUserCurrency(gameConfig.value.initialization.gameSettings.minStake),
            maxStake: formatToUserCurrency(gameConfig.value.initialization.gameSettings.maxStake),
        }),
        [],
    );

    useEffect(() => {
        const unsubscribeFromBalance = rgsBalanceEntity.store(gameId).init();
        setShouldValidateBalance(true);

        setMounted(true);
        eventManagerRef.current = new EventManager<LuckyLennyEvents>();

        eventManagerRef.current.on(LuckyLennyEvents.winAnimationStart, () => {
            // Sync balance on main game win animation start
            if (gameConfigSelectors.shouldSyncBalance(gameConfig.value)) {
                rgsBalanceEntity
                    .store(gameId)
                    .toggleSync(true)
                    .applyChange(totalWinAmountRef.current);
            }
        });
        eventManagerRef.current.on(LuckyLennyEvents.winAnimationEnd, () => {
            // Enable betting for bonus game respin when win animation finishes
            if (gameConfigSelectors.shouldEnableBonusWheelBetting(gameConfig.value)) {
                setOpenForBets(true);
            }
        });
        eventManagerRef.current.on(LuckyLennyEvents.totalWinAnimationStart, () => {
            // Sync balance on total win animation start
            rgsBalanceEntity.store(gameId).toggleSync(true).applyChange(totalWinAmountRef.current);
        });
        eventManagerRef.current.on(LuckyLennyEvents.newGame, () => {
            setOpenForBets(true);

            if (!pendingRespinRef.current) {
                stopAutoPlayOnInsufficientBalance(gameId);
                setShouldValidateBalance(true);
            }
        });

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(
            LuckyLennyEvents.switchGame,
            ({ gameId: luckyLennyGameId }) => {
                setLuckyLennyGame(luckyLennyGameId);

                if (
                    rgsAutoPlayEntity.store(gameId).value.isActive &&
                    rgsAutoPlayEntity.store(gameId).value.form.stopOnBonusGame &&
                    luckyLennyGameId !== LuckyLennyGameId.LennySlotGame
                ) {
                    rgsAutoPlayEntity.store(gameId).stopAutoPlay();
                }
            },
        );

        eventManagerRef.current.on<ILuckyLennySwitchGameAnimationEvent>(
            LuckyLennyEvents.switchGameAnimation,
            (animating) => {
                setSwitchGameAnimating(animating);
            },
        );

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(LuckyLennyEvents.switchGame, () => {
            setTotalWinAmount(totalWinAmountRef.current);
        });

        return () => {
            eventManagerRef.current = null;
            luckyLennySoundsService.stopAll();
            unsubscribeFromBalance();
            rgsAutoPlayEntity.store(gameId).reset();
        };
    }, []);

    useEffect(() => {
        if (!mounted || !eventManagerRef.current) {
            return undefined;
        }

        // Logger.enable(true, false, 'lenny'); //  -- if you want to enable logging

        if (isDebugMode) Logger.log('Debug mode enabled via URL parameter', 'lenny');

        // Set up loading state
        setLoading(true);

        // Initialize game experience
        experienceRef.current = new LennyExperience({
            canvas: canvasRef.current,
            enableDebug: isDebugMode,
            onLoadComplete: () => {
                // Hide the splash screen once loading is complete
                setLoading(false);
            },
            eventManager: eventManagerRef.current,
            width: canvasRef.current.parentElement.offsetWidth,
            height: canvasRef.current.parentElement.offsetHeight,
        });

        return () => {
            if (experienceRef.current) {
                experienceRef.current.dispose();
            }
        };
    }, [mounted, isDebugMode]); // Remove handleResize dependency to prevent remounting

    // --------------------------------------------------------------
    // Unified resize handler – handles both breakpoint changes and
    // browser resize events in a single place.
    // --------------------------------------------------------------
    useEffect(() => {
        let resizeTimerId: number | undefined;

        const handleResize = (): void => {
            if (experienceRef.current && canvasRef.current && canvasRef.current.parentElement) {
                experienceRef.current.handleResize(
                    isMobileView,
                    canvasRef.current.parentElement.offsetWidth,
                    canvasRef.current.parentElement.offsetHeight,
                );
            }
        };

        // Debounce using requestAnimationFrame
        const windowResize = (): void => {
            cancelAnimationFrame(resizeTimerId);
            resizeTimerId = requestAnimationFrame(handleResize);
        };

        window.addEventListener('resize', windowResize);

        // Perform initial sizing
        windowResize();

        return () => {
            cancelAnimationFrame(resizeTimerId);
            window.removeEventListener('resize', windowResize);
        };
    }, [isMobileView]);

    const progressSubscribeHandler = useCallback(
        (callback: (progress: number) => void) => {
            return LuckyLennyAssetLoader.subscribeToProgress((progress) => {
                callback(progress.percentage);
            });
        },
        [loading],
    );

    const handleAutoPlayAmounts = useCallback((response: IPlayResponse, stake: number) => {
        if (rgsAutoPlayEntity.store(gameId).value.isActive) {
            rgsAutoPlayEntity.store(gameId).updateAutoPlayResult(
                // don't add stake (loss) amount on respin or bonus
                gameConfig.value.config.lastRoundResult.bonusRounds.length ? 0 : stake,
                gameConfigSelectors.getTotalAmountWon(response),
            );
        }
    }, []);

    const handleFreePlayUpdates = (response: IPlayResponse) => {
        rgsFreePlayEntity
            .store(gameId)
            .updateStore(
                response.gameState.length > 1,
                gameConfigSelectors.getLastGameStateWinAmount(response),
                response.freePlayInfo,
                response.freePlaysAvailable,
            );

        if (response.freePlayInfo?.isLast) {
            eventManagerRef.current.trigger(LuckyLennyEvents.freePlayCompleted);
        }
    };

    const handleGameboardTileClick = useCallback(
        (symbolIndex: number) => {
            if (!eventManagerRef.current) {
                return;
            }

            setOpenForBets(false);
            const stake = freePlay.active ? freePlay.stake : betAmountEntity.store(gameId).value;

            gameApi
                .play({
                    url: gameConfig.value.initialization.backendUrl,
                    token: gameConfig.value.initialization.token,
                    stake,
                    symbolIndex,
                })
                .then((response) => {
                    Logger.warn('Bonus gameboard response received', 'lenny');

                    // TODO check header error-id
                    if (!response.gameState) {
                        const errorString = t.error('transaction_failed_bet_not_accepted');
                        Logger.error(errorString);
                        throw Error(errorString);
                    }

                    totalWinAmountRef.current = gameConfigSelectors.getTotalAmountWon(response);

                    gameConfig.update((draft) => {
                        draft.config.lastRoundResult = response.lastRoundResult;
                        draft.config.nextAction = response.nextAction;
                        draft.gameState = response.gameState;
                    });

                    handleFreePlayUpdates(response);

                    // Enabled betting for next gameboard clicking
                    if (gameConfigSelectors.shouldEnableBonusGameboardBetting(gameConfig.value)) {
                        setOpenForBets(true);
                    }

                    experienceRef.current.onBonusGameboardClickResponse(response, symbolIndex);

                    handleAutoPlayAmounts(response, stake);
                })
                .catch(handleError);
        },
        [freePlay.active],
    );

    const handleSpin = useCallback(
        (isPendingRespin = false) => {
            if (!eventManagerRef.current) {
                return;
            }

            luckyLennySoundsService.play(LuckyLennySound.SpinButton);
            eventManagerRef.current.trigger(LuckyLennyEvents.bonusWheelSpin);
            eventManagerRef.current.trigger(LuckyLennyEvents.spin);

            setOpenForBets(false);

            if (partnerSettingsStore.isRealtimeBalanceEnabled) {
                // Avoid updating balance with staged bet amount already deducted
                // Staged bet amount should be removed once the bet is resolved
                balanceStore.waitForUpdate(BALANCE_UPDATE_DELAY);
            }

            const stake = freePlay.active ? freePlay.stake : betAmountEntity.store(gameId).value;

            const shouldApplyBalanceChange =
                luckyLennyGameRef.current === LuckyLennyGameId.LennySlotGame &&
                !isPendingRespin &&
                !freePlay.active;

            if (shouldApplyBalanceChange) {
                rgsBalanceEntity.store(gameId).toggleSync(false).applyChange(-stake, true);
                setShouldValidateBalance(false);
            }

            const returnBalance = (): void => {
                if (shouldApplyBalanceChange) {
                    rgsBalanceEntity.store(gameId).toggleSync(true).applyChange(stake, true);
                    setShouldValidateBalance(true);
                }
            };

            gameApi
                .play({
                    url: gameConfig.value.initialization.backendUrl,
                    token: gameConfig.value.initialization.token,
                    stake,
                    useTicket: freePlay.active,
                })
                .then((response) => {
                    Logger.warn('OnSpin response received', 'lenny');

                    // TODO check header error-id
                    if (!response.gameState) {
                        // return stake on error
                        returnBalance();

                        const errorString = t.error('transaction_failed_bet_not_accepted');
                        Logger.error(errorString);
                        throw Error(errorString);
                    }

                    totalWinAmountRef.current = gameConfigSelectors.getTotalAmountWon(response);

                    gameConfig.update((draft) => {
                        draft.config.lastRoundResult = response.lastRoundResult;
                        draft.config.nextAction = response.nextAction;
                        draft.gameState = response.gameState;
                    });

                    handleFreePlayUpdates(response);

                    experienceRef.current.onSpin(response);

                    handleAutoPlayAmounts(response, stake);
                })
                .catch((err) => {
                    // return stake on error
                    returnBalance();
                    handleError(err);
                });
        },
        [freePlay.active],
    );

    const handleGameboardAutoPlay = useCallback(() => {
        if (!eventManagerRef.current) {
            return;
        }

        setOpenForBets(false);
        eventManagerRef.current.trigger(LuckyLennyEvents.bonusGameboardAutoPlay);
    }, []);

    const shouldSkipAutoPlay = (): boolean =>
        pendingRespin || luckyLennyGameRef.current !== LuckyLennyGameId.LennySlotGame;

    useAutoPlay({
        gameId,
        callback: handleSpin,
        options: {
            shouldSkipAutoPlay,
            isPaused: showFreePlayModal || pendingShowFreePlayModal || freePlay.isInitialModal,
        },
    });

    const handleTurbo = (): void => {
        const newTurbo = !isTurbo;
        eventManagerRef.current.trigger<ILuckyLennyTurboEvent>(LuckyLennyEvents.turbo, newTurbo);
        setIsTurbo(newTurbo);
    };

    const handleGameSwitchOnGameStart = useCallback(() => {
        const { lastRoundResult, nextAction } = gameConfig.value.config;

        switch (nextAction) {
            case NextAction.WHEEL: {
                rgsBalanceEntity.store(gameId).toggleSync(false);

                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennyWheelGame,
                    },
                );
                break;
            }

            case NextAction.GAMEBOARD: {
                rgsBalanceEntity.store(gameId).toggleSync(false);

                let symbols: number[];

                if (lastRoundResult?.type === 'gameboard') {
                    const setIndex = lastRoundResult.roundVariables?.setIndex ?? 0;
                    symbols = lastRoundResult.symbols?.map((index) =>
                        mapBoardSymbolIndexToMultiplier(index, setIndex),
                    );
                }

                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennyAztecGame,
                        symbols,
                    },
                );
                break;
            }

            default:
                // Default to Slot game

                if (lastRoundResult) {
                    experienceRef.current.onStartFromScratch(lastRoundResult.symbols);
                    // if slot game && lastRoundResult exists - pending respin action
                    eventManagerRef.current.trigger(LuckyLennyEvents.respin);
                    rgsBalanceEntity.store(gameId).toggleSync(false);
                }
                eventManagerRef.current.trigger<ILuckyLennySwitchGameEvent>(
                    LuckyLennyEvents.switchGame,
                    {
                        gameId: LuckyLennyGameId.LennySlotGame,
                    },
                );
                break;
        }
    }, []);

    // --------------------------------------------------------------
    // Loading-screen dismissal → start correct game based on
    // `nextAction` that BE might have sent in the *initialisation*
    // payload (open rounds carry-over).
    // if it will be no next action / gameCfg empty, then start slot game
    // --------------------------------------------------------------
    const handleLoadingDismiss = useCallback(() => {
        setShowSplashscreen(false);
        if (!eventManagerRef.current) return;

        const { lastRoundResult } = gameConfig.value.config;

        if (config.initialization.freePlayInfo?.used === 0) {
            rgsFreePlayEntity.store(gameId).requestModalShow();
        }

        const showModalOrStartGame = (): void => {
            if (lastRoundResult) {
                setShowUnfinishedGameModal(true);
            } else {
                handleGameSwitchOnGameStart();
            }
        };

        if (partnerSettingsStore.isBrazilJurisdiction) {
            modalsManager.toggle(ModalName.RGS_SETTINGS, SettingsModal, {
                gameId,
                replacements: howToPlayReplacements,
                onClose: () => {
                    showModalOrStartGame();
                },
            });
        } else {
            showModalOrStartGame();
        }

        experienceRef.current.handleResize(
            isMobileView,
            canvasRef.current.parentElement.offsetWidth,
            canvasRef.current.parentElement.offsetHeight,
        );
    }, [config]);

    const handleUnfinishedGameModalClick = () => {
        setShowUnfinishedGameModal(false);
        handleGameSwitchOnGameStart();
    };

    // Set up additional event handlers after functions are defined
    useEffect(() => {
        if (!eventManagerRef.current) return;

        eventManagerRef.current.on<ILuckyLennySwitchGameEvent>(
            LuckyLennyEvents.switchGame,
            (data) => {
                // enable betting when switching to bonus game, for main game there are different events
                if (data.gameId !== LuckyLennyGameId.LennySlotGame) {
                    setOpenForBets(true);
                }
            },
        );

        eventManagerRef.current.on<ILuckyLennyBonusGameboardClickEvent>(
            LuckyLennyEvents.bonusGameboardClick,
            (symbolIndex) => {
                handleGameboardTileClick(symbolIndex);
            },
        );

        eventManagerRef.current.on(LuckyLennyEvents.respin, () => {
            setPendingRespin(true);
            pendingRespinRef.current = true;
        });
    }, []);

    useEffect(() => {
        let timer: number;
        if (pendingRespin && openForBets) {
            // TODO move to "newGame"

            setShowRespinModal(true);

            timer = setTimeout(() => {
                setShowRespinModal(false);
                setPendingRespin(false);
                pendingRespinRef.current = false;
                handleSpin(true);
            }, RESPIN_MODAL_SHOW_TIME);
        }
        return () => clearTimeout(timer);
    }, [pendingRespin, openForBets]);

    useEffect(() => {
        // Stop autoplay if there is an error
        if (rgsAutoPlayEntity.store(gameId).value.isActive && error) {
            rgsAutoPlayEntity.store(gameId).stopAutoPlay();
        }
    }, [error]);

    const isAutoPlayEnabled = partnerSettingsStore.isAutoPlayEnabled(gameId);
    const isSlotGame = luckyLennyGame === LuckyLennyGameId.LennySlotGame;
    const isBalanceInsufficient = balance < betAmount;
    const isBettingDisabled =
        !openForBets || (!freePlay.active && isBalanceInsufficient && isSlotGame);

    useBackgroundMusic({ showSplashscreen, luckyLennyGame, isSwitchGameAnimating, openForBets });

    return (
        <div
            className={classNames(classes.gameContent, {
                [classes.loaded]: !loading,
                [classes.hideContent]: showSplashscreen,
            })}
        >
            {!!error && <ErrorModal message={error} />}
            {showUnfinishedGameModal && (
                <UnfinishedGameModal onButtonClick={handleUnfinishedGameModalClick} />
            )}
            {showFreePlayModal && <FreePlayModal onButtonClick={handleFreePlayModalDismiss} />}
            {showRespinModal && <RespinModal />}
            <RotateRestriction forced={isLandscape} />
            <TopNotifications
                message={topNotification}
                className={classNames(classes.topNotifications, classes.forceShowContent)}
            />
            {showSplashscreen && (
                <RGSLoadingScreen
                    loading={loading}
                    className={classes.forceShowContent}
                    progressSubscriber={progressSubscribeHandler}
                    onDismiss={handleLoadingDismiss}
                />
            )}
            <div className={classes.container}>
                {mounted && <canvas ref={canvasRef} className={classes.canvas} />}
            </div>
            {!isMaintenance && <LiveInfo gameId={gameId} noLiveIndicator />}

            {!isSwitchGameAnimating && (
                <Controls.Container
                    bonusLayout={!isSlotGame}
                    render={() => {
                        if (isLandscape) {
                            return (
                                <>
                                    <Burger gameId={gameId} replacements={howToPlayReplacements} />
                                    <PlayerInfo gameId={gameId} balance={balance} />
                                    <InfoMessage
                                        gameId={gameId}
                                        eventManagerRef={eventManagerRef}
                                        totalWinAmount={totalWinAmount}
                                    />
                                    {isSlotGame && (
                                        <StakeSelection
                                            gameId={gameId}
                                            amountsSequence={stakes}
                                            validateBalance={shouldValidateBalance}
                                            disabled={!openForBets || freePlay.active}
                                        />
                                    )}
                                    {luckyLennyGame === LuckyLennyGameId.LennyAztecGame ? (
                                        <AutoPick
                                            onClick={handleGameboardAutoPlay}
                                            disabled={!openForBets}
                                        />
                                    ) : (
                                        <Spin
                                            onClick={handleSpin}
                                            glow={openForBets}
                                            disabled={isBettingDisabled}
                                        />
                                    )}
                                    <Turbo
                                        onClick={handleTurbo}
                                        active={isTurbo}
                                        disabled={!openForBets}
                                        hidden={!isSlotGame}
                                    />
                                    {isAutoPlayEnabled && (
                                        <AutoPlay
                                            disabled={isBettingDisabled}
                                            gameId={gameId}
                                            hidden={!isSlotGame}
                                        />
                                    )}
                                    <Sound />
                                </>
                            );
                        }

                        return (
                            <>
                                <Controls.PortraitTopRow bonusLayout={!isSlotGame}>
                                    <InfoMessage
                                        gameId={gameId}
                                        eventManagerRef={eventManagerRef}
                                        totalWinAmount={totalWinAmount}
                                    />
                                    {isSlotGame && (
                                        <StakeSelection
                                            gameId={gameId}
                                            amountsSequence={stakes}
                                            validateBalance={shouldValidateBalance}
                                            disabled={!openForBets || freePlay.active}
                                        />
                                    )}
                                    {luckyLennyGame === LuckyLennyGameId.LennyAztecGame ? (
                                        <AutoPick
                                            onClick={handleGameboardAutoPlay}
                                            disabled={!openForBets}
                                        />
                                    ) : (
                                        <Spin
                                            onClick={handleSpin}
                                            glow={openForBets}
                                            disabled={isBettingDisabled}
                                        />
                                    )}
                                    {isSlotGame && (
                                        <Turbo
                                            onClick={handleTurbo}
                                            active={isTurbo}
                                            disabled={!openForBets}
                                        />
                                    )}
                                    {isAutoPlayEnabled && isSlotGame && (
                                        <AutoPlay disabled={isBettingDisabled} gameId={gameId} />
                                    )}
                                </Controls.PortraitTopRow>
                                <Controls.PortraitBottomRow>
                                    <Burger gameId={gameId} replacements={howToPlayReplacements} />
                                    <PlayerInfo gameId={gameId} balance={balance} />
                                    <Sound />
                                </Controls.PortraitBottomRow>
                            </>
                        );
                    }}
                />
            )}
        </div>
    );
};
