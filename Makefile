#! /usr/bin/make -f
args = $(foreach a,$($(subst -,_,$1)_args),$(if $(value $a),$a="$($a)"))
name ?= iframe
file ?= ./docker/docker-compose.yml
colon := :
$(colon) := :
remotes := $(shell ls -d apps/iframe-remotes/*/ | xargs -n1 basename | tr '\n' ',' | sed 's/,$$//')
dr =

bgproxy:
	docker start bgproxy

deps:
	# If you are not on macOS: https://nodejs.org/en/download/package-manager/
	brew install node@20 ;\
	brew install pnpm ;\
	pnpm i -g nx ;\
	pnpm i

dev-certs:
	nx run iframe-server:dev-certs ;\

build:
	nx run-many -t build --projects=tag:scope:iframe-classic --parallel=8 --configuration=production --verbose && exit 0 ;\

build-qa:
	nx run-many -t build --projects=tag:role:host,tag:role:remote --parallel=8 --configuration=testing --verbose ;\
	nx run-many -t build --projects=tag:stack:be --parallel=8 --configuration=production --verbose

build-docker:
	docker compose -f $(file) -p $(name) build

run:
	pnpm i ;\
	make build ;\
	make bgproxy ;\
	make build-docker ;\
	docker compose -f $(file) -p $(name) up -d

stop:
	docker compose -f $(file) -p $(name) down

restart:
	make stop ;\
	make run

# starting the app in production mode using iframe-server
run-prod:
	pnpm i ;\
	make build ;\
	nx run iframe-server:start

# running the iframe-shell with remotes served dynamically
run-dev:
	pnpm i ;\
	nx run iframe-shell:serve

# // TODO: move app related makefiles to apps/[app]/Makefile
# // TODO: implement aliases to app Makefile
rss-run-dev:
	pnpm i ;\
	nx run-many --target=serve --projects=tag:scope:retail-skyward-screen --verbose

rss-build:
	nx run retail-skyward-screen/server:build:production ;\
 	nx run retail-skyward-screen/client:build:production

rss-run-prod:
	pnpm i ;\
    make rss-build ;\
    nx run retail-skyward-screen/server:start


check-all:
	make lint-all ;\
	make test-all

TEST_SCRIPT = nx run-many --target=test --projects=tag:scope:iframe-classic,tag:type:library,tag:scope:iframe-chat --parallel=8 --verbose

test-all:
	$(TEST_SCRIPT)

test-update:
	$(TEST_SCRIPT) --updateSnapshot

lint-ts:
	nx run-many --target=lint --projects=tag:scope:iframe-classic,tag:type:library,tag:scope:iframe-chat --parallel=8 --verbose --max-warnings=0

lint-typecheck:
	nx run-many --target=typecheck --projects=tag:scope:iframe-classic,tag:type:library,tag:scope:iframe-chat --parallel=8 --verbose

lint-style:
	nx run-many --target=stylelint --projects=tag:stack:fe,tag:type:library,tag:scope:iframe-chat --parallel=8 --verbose --color --max-warnings=0

lint-all:
	make lint-ts ;\
	make lint-typecheck ;\
	make lint-style

remote-dev:
	ngrok http https://localhost:8080 --host-header="localhost:8080"

remote-docker:
	ngrok http https://webiframe.betgames.test --host-header="webiframe.betgames.test"

# Clearing local cache
reset:
	rm -rf dist/ ;\
    nx reset

# generating types for supabase in the root directory of the projects
# the content of this file should be copied to the desired location
supabase-types:
	npx supabase gen types typescript --project-id epcoggacpukhlyzikvsc --schema public > database.types.ts
